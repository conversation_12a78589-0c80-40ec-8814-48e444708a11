# Log rotation
@daily /usr/sbin/logrotate /etc/logrotate.d/app


# Clear unused uploads (every day)
0 0 * * * cd /usr/src/app/apps/cron && npm run -s clear-unused-uploads >> /var/log/cron.log 2>&1

# Process due subscriptions (every day at 1:00 AM)
0 1 * * * cd /usr/src/app/apps/cron && npm run -s process-subscriptions >> /var/log/cron.log 2>&1

# Process free trials (every day at 2:00 AM)
0 2 * * * cd /usr/src/app/apps/cron && npm run -s process-free-trials >> /var/log/cron.log 2>&1
