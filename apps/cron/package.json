{"name": "@coheadcoaching/cron", "scripts": {"build": "tsc && tsc-alias", "type-check": "tsc --noEmit", "clear-unused-uploads": "node dist/cron/clear-unused-uploads/index.js", "dev:clear-unused-uploads": "npm run build && npm run clear-unused-uploads", "sample": "node dist/cron/sample/index.js", "dev:sample": "npm run build && npm run sample", "process-subscriptions": "node dist/cron/process-subscriptions/index.js", "dev:process-subscriptions": "npm run build && npm run process-subscriptions", "process-free-trials": "node dist/cron/process-free-trials/index.js", "dev:process-free-trials": "npm run build && npm run process-free-trials", "lint": "eslint .", "lint:fix": "eslint --fix .", "prettier": "prettier --check \"**/*.{js,jsx,ts,tsx}\"", "prettier:fix": "prettier --write \"**/*.{js,jsx,ts,tsx}\"", "postinstall": "prisma generate"}, "keywords": [], "author": "", "license": "MIT", "dependencies": {"@aws-sdk/client-s3": "^3.549.0", "@coheadcoaching/lib": "*", "@prisma/client": "^5.12.1", "@t3-oss/env-core": "^0.10.0", "@types/node": "^20.11.19", "chalk": "^5.3.0", "custom-prettier-config": "*", "date-fns": "^4.1.0", "dotenv": "^16.4.4", "eslint-config-custom": "*", "prisma": "^5.12.1", "tsc-alias": "^1.8.8", "typescript": "^5.3.3", "zod": "^3.22.4"}, "prisma": {"schema": "../app/prisma/schema.prisma"}, "type": "module"}