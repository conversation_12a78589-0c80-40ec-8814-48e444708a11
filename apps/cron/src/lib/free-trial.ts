import { addDays } from "date-fns"

import { logger } from "@coheadcoaching/lib"
import { BillingPeriod, SubscriptionStatus } from "@prisma/client"

import { sendFreeTrialReminderEmailViaWebhook } from "./email-webhook"
import { env } from "./env"
import { processSubsequentRecurringPayment } from "./mangopay"
import { prisma } from "./prisma"

const FREE_TRIAL_REMINDER_DAYS_BEFORE = 2

/**
 * Get free trial subscriptions that need reminder emails
 */
async function getTrialsNeedingReminders() {
  const reminderDate = addDays(new Date(), FREE_TRIAL_REMINDER_DAYS_BEFORE)

  const subscriptions = await prisma.subscription.findMany({
    where: {
      isFreeTrial: true,
      trialReminderSent: false,
      freeTrialEndDate: {
        lte: reminderDate,
      },
      status: SubscriptionStatus.ACTIVE,
    },
    include: {
      user: {
        select: {
          id: true,
          email: true,
          username: true,
          name: true,
        },
      },
      plan: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  })

  return subscriptions
}

/**
 * Get expired free trial subscriptions that need to be processed
 */
async function getExpiredTrials() {
  const now = new Date()

  const subscriptions = await prisma.subscription.findMany({
    where: {
      isFreeTrial: true,
      freeTrialEndDate: {
        lte: now,
      },
      status: SubscriptionStatus.ACTIVE,
    },
    include: {
      user: {
        select: {
          id: true,
          email: true,
          username: true,
          name: true,
          mangopayUserId: true,
        },
      },
      plan: {
        select: {
          id: true,
          name: true,
          monthlyPrice: true,
          annualPrice: true,
        },
      },
    },
  })

  return subscriptions
}

/**
 * Send free trial reminder emails
 */
export async function sendFreeTrialReminders() {
  logger.log("Starting free trial reminder job")

  try {
    const subscriptions = await getTrialsNeedingReminders()

    logger.log(`Found ${subscriptions.length} trials needing reminders`)

    for (const subscription of subscriptions) {
      try {
        if (!subscription.user.email) {
          logger.warn("User has no email address", { userId: subscription.userId })
          continue
        }

        const userName = subscription.user.username || subscription.user.name || "Utilisateur"
        const trialEndDate = subscription.freeTrialEndDate!.toLocaleDateString("fr-FR", {
          day: "numeric",
          month: "long",
          year: "numeric",
        })

        // Send reminder email via webhook
        await sendFreeTrialReminderEmailViaWebhook({
          to: subscription.user.email,
          userName,
          planName: subscription.plan.name,
          trialEndDate,
          manageSubscriptionUrl: `${env.NEXT_PUBLIC_BASE_URL}/profile/subscription`,
          logoUrl: `${env.NEXT_PUBLIC_BASE_URL}/logo.svg`,
        })

        // Mark reminder as sent
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: { trialReminderSent: true },
        })

        logger.log("Free trial reminder sent", {
          subscriptionId: subscription.id,
          userEmail: subscription.user.email,
        })
      } catch (error) {
        logger.error("Failed to send free trial reminder", {
          subscriptionId: subscription.id,
          error,
        })
      }
    }

    logger.log("Free trial reminder job completed", { processed: subscriptions.length })
  } catch (error) {
    logger.error("Free trial reminder job failed", error)
    throw error
  }
}

/**
 * Process expired free trials by initiating the first real payment
 */
async function processExpiredFreeTrials() {
  logger.log("Starting expired free trials processing job")

  try {
    const subscriptions = await getExpiredTrials()

    logger.log(`Found ${subscriptions.length} expired trials to process`)

    for (const subscription of subscriptions) {
      try {
        if (!subscription.mangopayRecurringRegistrationId) {
          logger.error("Subscription missing recurring registration ID", {
            subscriptionId: subscription.id,
          })
          continue
        }

        const baseAmount =
          subscription.billingPeriod === BillingPeriod.MONTHLY
            ? subscription.plan.monthlyPrice
            : subscription.plan.annualPrice

        // Apply promotional code discounts if applicable
        let finalAmount = baseAmount
        let appliedPromoCode = null

        // Get subscription with promotional code usages
        const subscriptionWithPromo = await prisma.subscription.findUnique({
          where: { id: subscription.id },
          include: {
            promotionalCodeUsages: {
              include: {
                promotionalCode: true,
              },
            },
          },
        })

        if (subscriptionWithPromo?.promotionalCodeUsages) {
          for (const usage of subscriptionWithPromo.promotionalCodeUsages) {
            const promoCode = usage.promotionalCode

            // Check if promotional code is still valid and applicable
            if (
              promoCode.isActive &&
              (!promoCode.validUntil || promoCode.validUntil > new Date()) &&
              (!promoCode.validFrom || promoCode.validFrom <= new Date())
            ) {
              // Count how many payments this user has already used this promotional code for
              const userPaymentCount = await prisma.payment.count({
                where: {
                  subscription: {
                    userId: subscription.userId,
                    promotionalCodeUsages: {
                      some: {
                        promotionalCodeId: promoCode.id,
                      },
                    },
                  },
                  status: "SUCCEEDED",
                },
              })

              // Check if user is still eligible for discount (hasn't exceeded paymentCount)
              if (userPaymentCount < promoCode.paymentCount) {
                // Apply discount
                if (promoCode.discountType === "PERCENTAGE") {
                  const discountAmount = Math.round((baseAmount * promoCode.discountValue) / 100)
                  finalAmount = Math.max(0, baseAmount - discountAmount)
                } else if (promoCode.discountType === "FIXED_AMOUNT") {
                  finalAmount = Math.max(0, baseAmount - promoCode.discountValue)
                }

                appliedPromoCode = promoCode
                logger.log(
                  `Applied promotional code ${promoCode.code} to trial expiration for subscription ${subscription.id}. Original: ${baseAmount}¢, Final: ${finalAmount}¢. User payment count: ${userPaymentCount}/${promoCode.paymentCount}`
                )
                break // Apply only the first valid promotional code
              }
            }
          }
        }

        // Process the first real payment using the existing mangopay function
        const paymentResult = await processSubsequentRecurringPayment({
          recurringPayinRegistrationId: subscription.mangopayRecurringRegistrationId,
          amount: finalAmount,
          currency: "EUR",
          tag: `CoheadCoaching Trial Expiration - Sub: ${subscription.id}${appliedPromoCode ? `, Promo: ${appliedPromoCode.code}` : ""}`.substring(
            0,
            255
          ),
          statementDescriptor: `ABO ${subscription.plan.name.substring(0, 6)}`,
        })

        // Create payment record
        await prisma.payment.create({
          data: {
            subscriptionId: subscription.id,
            amount: finalAmount,
            currency: "EUR",
            status: paymentResult.Status === "SUCCEEDED" ? "SUCCEEDED" : "PENDING",
            mangopayPayinId: paymentResult.Id,
          },
        })

        // Log promotional code usage if one was applied
        if (appliedPromoCode) {
          logger.log(
            `Promotional code ${appliedPromoCode.code} applied to trial expiration for subscription ${subscription.id}`
          )
        }

        // Update subscription to mark trial as completed
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            isFreeTrial: false,
            freeTrialEndDate: null,
          },
        })

        logger.log("Expired trial processed successfully", {
          subscriptionId: subscription.id,
          userId: subscription.userId,
          paymentStatus: paymentResult.Status,
        })
      } catch (error) {
        logger.error("Failed to process expired trial", {
          subscriptionId: subscription.id,
          error,
        })

        // Mark subscription as failed if payment fails
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            status: SubscriptionStatus.FAILED,
          },
        })
      }
    }

    logger.log("Expired free trials processing job completed", { processed: subscriptions.length })
  } catch (error) {
    logger.error("Expired free trials processing job failed", error)
    throw error
  }
}

/**
 * Combined job that runs both reminder and expiration processing
 */
export async function processFreeTrials() {
  logger.log("Starting combined free trial processing")

  try {
    // Run reminder job first
    await sendFreeTrialReminders()

    // Then process expirations
    await processExpiredFreeTrials()

    logger.log("Combined free trial processing completed successfully")
  } catch (error) {
    logger.error("Combined free trial processing failed", error)
    throw error
  }
}
