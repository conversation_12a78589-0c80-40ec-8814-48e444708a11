#!/usr/bin/env node

import { exit } from "process"

import { processFreeTrials } from "@/lib/free-trial"
import { logger } from "@coheadcoaching/lib"

async function main() {
  const maxDurationWarning = 1000 * 60 * 5 // 5 minutes
  const name = "ProcessFreeTrials"
  const now = new Date()
  logger.prefix = () => `[${new Date().toLocaleString()}] `
  await processFreeTrials().catch((err) => {
    logger.error(
      `${name} started at ${now.toLocaleString()} and failed after ${new Date().getTime() - now.getTime()}ms`
    )
    throw err
  })
  const took = new Date().getTime() - now.getTime()
  if (took > maxDurationWarning) logger.warn(`${name} took ${took}ms`)

  exit(0)
}

main()
