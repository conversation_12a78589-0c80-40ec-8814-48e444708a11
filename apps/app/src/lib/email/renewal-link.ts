import { getFullLogoUrl } from "@/constants/medias"
import { env } from "@/lib/env"
import { i18n, Locale } from "@/lib/i18n-config"
import { _getDictionary } from "@/lib/langs"
import { sendMail } from "@/lib/mailer"
import { logger } from "@coheadcoaching/lib"
import SubscriptionRenewalLink from "@coheadcoaching/transactional/emails/subscription-renewal-link"
import { BillingPeriod } from "@prisma/client"
import { render } from "@react-email/render"

interface RenewalLinkEmailData {
  userEmail: string
  userName: string
  userLocale?: string
  token: string
  subscription: {
    id: string
    plan: {
      name: string
    }
    billingPeriod: BillingPeriod
  }
  expiresAt: Date
}

export async function sendRenewalLinkEmail(data: RenewalLinkEmailData) {
  try {
    if (!env.NEXT_PUBLIC_ENABLE_MAILING_SERVICE) {
      logger.warn("Email service is disabled, skipping renewal link email")
      return { success: false, reason: "Email service disabled" }
    }

    const { userEmail, userName, userLocale, token, subscription, expiresAt } = data

    // Build renewal URL
    const renewalUrl = `${env.NEXT_PUBLIC_BASE_URL}/subscription/renew/${token}`

    // Get user locale or default
    const locale = (userLocale as Locale | null) ?? i18n.defaultLocale

    // Get translations (you may need to add these to your dictionary)
    const dictionary = await _getDictionary("transactionals", locale, {
      hey: true,
      footer: true,
      // Add renewal-specific translations
      subscriptionRenewalRequired: true,
      renewMySubscription: true,
      subscriptionRenewalDescription: true,
      subscriptionRenewalWarning: true,
      subscriptionRenewalInstructions: true,
    }).catch(() => ({
      // Fallback translations
      hey: "Bonjour",
      footer:
        "Cet e-mail vous a été envoyé dans le cadre de nos services d'abonnement. Si vous avez des questions, veuillez nous contacter à",
      subscriptionRenewalRequired: "Renouvellement d'abonnement requis",
      renewMySubscription: "Renouveler mon abonnement",
      subscriptionRenewalDescription:
        "Votre abonnement nécessite une action de votre part pour continuer. Une re-authentification bancaire est requise pour maintenir votre service actif.",
      subscriptionRenewalWarning:
        "⚠️ Important : Si aucune action n'est effectuée avant la date d'expiration, votre abonnement sera suspendu et vous perdrez l'accès à nos services.",
      subscriptionRenewalInstructions:
        "En cliquant sur le bouton ci-dessus, vous serez redirigé vers une page sécurisée où vous pourrez confirmer le renouvellement de votre abonnement. Cette étape est nécessaire pour respecter les réglementations bancaires européennes (PSD2).",
    }))

    // Format billing period
    const billingPeriodText = subscription.billingPeriod === BillingPeriod.MONTHLY ? "Mensuel" : "Annuel"

    // Format expiration date
    const expirationDateText = expiresAt.toLocaleDateString(locale === "fr" ? "fr-FR" : "en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })

    // Create email component
    const emailComponent = SubscriptionRenewalLink({
      renewalLink: renewalUrl,
      previewText: "Action requise : Renouvellement de votre abonnement",
      logoUrl: getFullLogoUrl(env.NEXT_PUBLIC_BASE_URL || ""),
      name: userName,
      supportEmail: env.SUPPORT_EMAIL ?? "",
      titleText: dictionary.subscriptionRenewalRequired as string,
      footerText: dictionary.footer,
      contentTitle: dictionary.subscriptionRenewalDescription as string,
      actionText: dictionary.renewMySubscription as string,
      heyText: dictionary.hey,
      planName: subscription.plan.name,
      billingPeriod: billingPeriodText,
      expirationDate: expirationDateText,
      warningText: dictionary.subscriptionRenewalWarning as string,
      instructionsText: dictionary.subscriptionRenewalInstructions as string,
    })

    // Render email
    const text = render(emailComponent, { plainText: true })
    const html = render(emailComponent)

    // Send email
    await sendMail({
      to: userEmail.toLowerCase(),
      subject: dictionary.subscriptionRenewalRequired as string,
      text,
      html,
    })

    logger.log(`Renewal link email sent successfully to ${userEmail} for subscription ${subscription.id}`)

    return { success: true }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`Failed to send renewal link email: ${errorMessage}`, { error })

    return { success: false, reason: errorMessage }
  }
}

// Helper function for cron job usage
export async function sendRenewalLinkEmailFromSubscription({
  userEmail,
  userName,
  token,
  subscription,
  expiresAt,
  userLocale,
}: {
  userEmail: string
  userName: string
  token: string
  subscription: {
    id: string
    plan: { name: string }
    billingPeriod: BillingPeriod
  }
  expiresAt: Date
  userLocale?: string
}) {
  return sendRenewalLinkEmail({
    userEmail,
    userName,
    userLocale,
    token,
    subscription,
    expiresAt,
  })
}
