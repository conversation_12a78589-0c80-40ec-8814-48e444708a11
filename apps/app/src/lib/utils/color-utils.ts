function hexToHsl(hex: string): { h: number; s: number; l: number } {
  hex = hex.replace(/^#/, "")
  const r = parseInt(hex.substring(0, 2), 16) / 255
  const g = parseInt(hex.substring(2, 4), 16) / 255
  const b = parseInt(hex.substring(4, 6), 16) / 255

  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  let h = 0,
    s = 0
  const l = (max + min) / 2

  if (max !== min) {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0)
        break
      case g:
        h = (b - r) / d + 2
        break
      case b:
        h = (r - g) / d + 4
        break
    }
    h /= 6
  }

  return {
    h: Math.round(h * 360),
    s: Math.round(s * 100),
    l: Math.round(l * 100),
  }
}

// Calculate relative luminance for contrast ratio calculations
function getLuminance(r: number, g: number, b: number): number {
  const [rs, gs, bs] = [r, g, b].map((c) => {
    c = c / 255
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
  })
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs
}

// Calculate contrast ratio between two colors
function getContrastRatio(
  color1: { r: number; g: number; b: number },
  color2: { r: number; g: number; b: number }
): number {
  const lum1 = getLuminance(color1.r, color1.g, color1.b)
  const lum2 = getLuminance(color2.r, color2.g, color2.b)
  const brightest = Math.max(lum1, lum2)
  const darkest = Math.min(lum1, lum2)
  return (brightest + 0.05) / (darkest + 0.05)
}

// Convert HSL to RGB
function hslToRgb(h: number, s: number, l: number): { r: number; g: number; b: number } {
  h = h / 360
  s = s / 100
  l = l / 100

  const c = (1 - Math.abs(2 * l - 1)) * s
  const x = c * (1 - Math.abs(((h * 6) % 2) - 1))
  const m = l - c / 2

  let r = 0,
    g = 0,
    b = 0

  if (0 <= h && h < 1 / 6) {
    r = c
    g = x
    b = 0
  } else if (1 / 6 <= h && h < 2 / 6) {
    r = x
    g = c
    b = 0
  } else if (2 / 6 <= h && h < 3 / 6) {
    r = 0
    g = c
    b = x
  } else if (3 / 6 <= h && h < 4 / 6) {
    r = 0
    g = x
    b = c
  } else if (4 / 6 <= h && h < 5 / 6) {
    r = x
    g = 0
    b = c
  } else if (5 / 6 <= h && h < 1) {
    r = c
    g = 0
    b = x
  }

  return {
    r: Math.round((r + m) * 255),
    g: Math.round((g + m) * 255),
    b: Math.round((b + m) * 255),
  }
}

// Find optimal text color with good contrast
function findOptimalTextColor(
  bgH: number,
  bgS: number,
  bgL: number,
  isDark: boolean
): { h: number; s: number; l: number } {
  const targetContrast = 4.5 // WCAG AA standard
  const bgRgb = hslToRgb(bgH, bgS, bgL)

  // Try different lightness values to find good contrast
  for (let l = isDark ? 95 : 5; isDark ? l >= 5 : l <= 95; isDark ? (l -= 5) : (l += 5)) {
    const textRgb = hslToRgb(bgH, Math.min(100, bgS * 1.2), l)
    const contrast = getContrastRatio(bgRgb, textRgb)

    if (contrast >= targetContrast) {
      return { h: bgH, s: Math.min(100, bgS * 1.2), l }
    }
  }

  // Fallback to high contrast
  return { h: bgH, s: Math.min(100, bgS * 1.2), l: isDark ? 95 : 5 }
}

export function generateBadgePalette(hex: string) {
  const { h, s, l } = hexToHsl(hex)

  // Light theme colors
  const lightBg = `${h} ${s}% ${l}%` // User-selected color
  const lightTextColor = findOptimalTextColor(h, s, l, false)
  const lightText = `${lightTextColor.h} ${lightTextColor.s}% ${lightTextColor.l}%`

  // Dark theme colors (invert lightness)
  const darkBgL = Math.max(0, 100 - l)
  const darkBg = `${h} ${s}% ${darkBgL}%`
  const darkTextColor = findOptimalTextColor(h, s, darkBgL, true)
  const darkText = `${darkTextColor.h} ${darkTextColor.s}% ${darkTextColor.l}%`

  return {
    "100-light": lightBg,
    light: lightText,
    "100-dark": darkBg,
    dark: darkText,
  }
}

// Generate badge colors for database storage
export function generateBadgeColorsForDB(hex: string) {
  const { h, s, l } = hexToHsl(hex)

  // Light theme colors
  const backgroundLight = `${h} ${s}% ${l}%`
  const lightTextColor = findOptimalTextColor(h, s, l, false)
  const textLight = `${lightTextColor.h} ${lightTextColor.s}% ${lightTextColor.l}%`

  // Dark theme colors
  const darkBgL = Math.max(0, 100 - l)
  const backgroundDark = `${h} ${s}% ${darkBgL}%`
  const darkTextColor = findOptimalTextColor(h, s, darkBgL, true)
  const textDark = `${darkTextColor.h} ${darkTextColor.s}% ${darkTextColor.l}%`

  return {
    primaryColor: hex,
    backgroundLight,
    textLight,
    backgroundDark,
    textDark,
  }
}

// Load badge colors from database format
export function loadBadgeColorsFromDB(badge: {
  primaryColor: string
  backgroundLight: string
  textLight: string
  backgroundDark: string
  textDark: string
}) {
  return {
    "100-light": badge.backgroundLight,
    light: badge.textLight,
    "100-dark": badge.backgroundDark,
    dark: badge.textDark,
  }
}
