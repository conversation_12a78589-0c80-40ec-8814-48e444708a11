function hexToHsl(hex: string): { h: number; s: number; l: number } {
  hex = hex.replace(/^#/, "")
  const r = parseInt(hex.substring(0, 2), 16) / 255
  const g = parseInt(hex.substring(2, 4), 16) / 255
  const b = parseInt(hex.substring(4, 6), 16) / 255

  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  let h = 0,
    s = 0
  const l = (max + min) / 2

  if (max !== min) {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0)
        break
      case g:
        h = (b - r) / d + 2
        break
      case b:
        h = (r - g) / d + 4
        break
    }
    h /= 6
  }

  return {
    h: Math.round(h * 360),
    s: Math.round(s * 100),
    l: Math.round(l * 100),
  }
}

export function generateBadgePalette(hex: string) {
  const { h, s, l } = hexToHsl(hex)

  // Light
  const lightBg = `${h} ${s}% ${l}%` // choisi par user
  const lightText = `${h} ${Math.min(100, s * 1.2)}% ${Math.max(0, l - 45)}%`

  // Dark (inversion de la luminosité)
  const darkBg = `${h} ${s}% ${Math.max(0, 100 - l)}%`
  const darkText = `${h} ${Math.min(100, s * 1.2)}% ${Math.min(100, 100 - l + 45)}%`

  return {
    "100-light": lightBg,
    light: lightText,
    "100-dark": darkBg,
    dark: darkText,
  }
}
