//! Only client-side code

import { signIn } from "next-auth/react"
import { toast } from "react-toastify"
import * as z from "zod"

import { logger } from "@coheadcoaching/lib"

import { signInSchema } from "../../api/auth/schemas"
import { TDictionary } from "../langs"

export const handleSignError = (
  error: string,
  dictionary: TDictionary<{
    errors: {
      wrongProvider: true
    }
    unknownError: true
  }>
) => {
  if (error == "OAuthAccountNotLinked") {
    toast.error(dictionary.errors.wrongProvider)
  } else {
    toast.error(dictionary.unknownError)
  }
}

export const handleSignIn = async ({
  data,
  callbackUrl,
  dictionary,
  depth = 0,
  getOtpCode,
}: {
  data: z.infer<ReturnType<typeof signInSchema>>
  callbackUrl: string
  dictionary: TDictionary<{
    errors: {
      invalidCredentials: true
      otpInvalid: true
      wrongProvider: true
    }
    unknownError: true
  }>
  depth?: number
  getOtpCode: () => Promise<string | null>
}) => {
  return new Promise<boolean>(async (resolve, reject) => {
    logger.debug("Signing in with credentials", data)
    try {
      const res = await signIn("credentials", {
        redirect: false,
        email: data.email,
        password: data.password,
        callbackUrl,
        otp: data.otp ?? undefined,
      })
      logger.log("AAA", res)
      if (!res?.error) {
        logger.debug("Sign in successful pushing to", callbackUrl)
        //? Navigate without the router due to some cache issue.
        window.location.href = callbackUrl
        resolve(true)
      } else {
        logger.log("Sign in failed", res)
        if (res.error === "AccessDenied") {
          if (depth === 0) {
            const otp = await getOtpCode()
            if (otp === null) {
              throw new Error(dictionary.unknownError)
            }
            const res = await handleSignIn({
              data: { ...data, otp },
              callbackUrl,
              dictionary,
              depth: depth + 1,
              getOtpCode,
            })
            resolve(res)
          } else {
            throw new Error(dictionary.errors.otpInvalid)
          }
        }
        if (typeof res.error === "string") {
          if (res.error === dictionary.errors.wrongProvider) throw new Error(res.error)
        }
        throw new Error(dictionary.errors.invalidCredentials)
      }
    } catch (error) {
      if (depth > 0) {
        reject(error)
        return
      }
      logger.error(error)
      if (error instanceof Error) {
        toast.error(error.message)
      } else {
        toast.error(dictionary.unknownError)
      }
      reject(error)
    } finally {
      resolve(false)
    }
  })
}
