import { Session } from "next-auth"
import superjson from "superjson"
import { Zod<PERSON>rror } from "zod"

import { getAuth<PERSON><PERSON> } from "@/components/auth/require-auth"
import { env } from "@/lib/env"
import { User, UserRole } from "@prisma/client"
import { initTRPC } from "@trpc/server"

import { apiRateLimiter } from "../rate-limit"
import { Context } from "../trpc/context"
import { ApiError } from "../utils/server-utils"

/**
 * Initialization of tRPC backend
 * Should be done only once per backend!
 */
const t = initTRPC.context<Context>().create({
  transformer: superjson,
  errorFormatter(opts) {
    const { shape, error } = opts
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError: error.code === "BAD_REQUEST" && error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    }
  },
})
/**
 * Export reusable router and procedure helpers
 * that can be used throughout the router
 */
export const createCallerFactory = t.createCallerFactory
export const router = t.router
export const middleware = t.middleware
const hasRateLimit = middleware(async (opts) => {
  if (opts.ctx.req) {
    const { headers } = await apiRateLimiter(opts.ctx.req)
    return opts.next({
      ctx: {
        Headers: headers,
      },
    })
  }
  return opts.next()
})
export const publicProcedure = t.procedure.use(hasRateLimit)
const isAuthenticated = middleware(async (opts) => {
  const { session } = await getAuthApi()

  if (!session) {
    await ApiError("unauthorized", "UNAUTHORIZED")
  }

  return opts.next({
    ctx: {
      ...opts.ctx,
      session,
    },
  })
})
const hasVerifiedEmail = middleware(async (opts) => {
  const { ctx } = opts
  const session = ctx.session as (Session & { user: Omit<User, "password"> }) | null
  if (!session || (!session.user.emailVerified && env.NEXT_PUBLIC_ENABLE_MAILING_SERVICE === true)) {
    await ApiError("emailNotVerified", "UNAUTHORIZED", {
      redirect: false,
    })
  }
  return opts.next()
})

const isNotFrozen = middleware(async (opts) => {
  const { ctx } = opts
  const session = ctx.session as (Session & { user: Omit<User, "password"> }) | null

  if (!session) {
    await ApiError("unauthorized", "UNAUTHORIZED")
  }

  // Check if user account is frozen
  if (session?.user.isFrozen) {
    await ApiError("accountFrozen", "FORBIDDEN", {
      redirect: false,
    })
  }

  return opts.next()
})

// New middleware for checking if user has any of the specified roles
const hasRoles = (allowedRoles: UserRole[]) =>
  middleware(async (opts) => {
    const { ctx } = opts
    const session = ctx.session as (Session & { user: Omit<User, "password"> }) | null

    if (!session) {
      await ApiError("unauthorized", "UNAUTHORIZED")
    }

    // Check if user has ADMIN role (which gives access to everything)
    if (session?.user.role === "ADMIN" || session?.user.roles?.includes("ADMIN")) {
      return opts.next()
    }

    // Check if user has any of the allowed roles
    const userRoles = session?.user.roles || []
    const hasRequiredRole = allowedRoles.some((role) => userRoles.includes(role))

    if (!hasRequiredRole) {
      await ApiError("forbidden", "FORBIDDEN", {
        redirect: false,
      })
    }

    return opts.next({
      ctx: {
        ...opts.ctx,
        userRoles,
      },
    })
  })
export const authenticatedProcedure = publicProcedure.use(isAuthenticated).use(hasVerifiedEmail).use(isNotFrozen)
export const authenticatedNoEmailVerificationProcedure = publicProcedure.use(isAuthenticated)
export const authenticatedActiveProcedure = publicProcedure.use(isAuthenticated).use(hasVerifiedEmail).use(isNotFrozen)

// New role-based procedures
export const savAuthenticatedProcedure = publicProcedure
  .use(isAuthenticated)
  .use(hasRoles(["SAV"]))
  .use(isNotFrozen)
export const modoAuthenticatedProcedure = publicProcedure
  .use(isAuthenticated)
  .use(hasRoles(["MODO"]))
  .use(isNotFrozen)
export const iaBuilderAuthenticatedProcedure = publicProcedure
  .use(isAuthenticated)
  .use(hasRoles(["IA_BUILDER"]))
  .use(isNotFrozen)
export const adminAuthenticatedProcedure = publicProcedure
  .use(isAuthenticated)
  .use(hasRoles(["ADMIN"]))
  .use(isNotFrozen)
