import { TDictionary } from "@/lib/langs"

export const rolesAsObject = {
  admin: "ADMIN",
  user: "USER",
  sav: "SAV",
  modo: "MODO",
  iaBuilder: "IA_BUILDER",
} as const

export const roleLabels = {
  ADMIN: "Administrateur (complet)",
  USER: "Utilisateur",
  SAV: "Service Après-Vente",
  MODO: "Modérateur",
  IA_BUILDER: "Créateur d'IA",
} as const

export const roleDescriptions = {
  ADMIN: "Accès total à tous les modules",
  USER: "Accès utilisateur standard",
  SAV: "Gestion des tickets SAV (voir, répondre, clore)",
  MODO: "Gestion des profils utilisateurs, abonnements et plans",
  IA_BUILDER: "Création et gestion des Agents IA (skills, badges, agents)",
} as const

export const appTitle = (dictionary: TDictionary<{ app: { name: true } }>) => dictionary.app.name
export const appDescription = (dictionary: TDictionary<{ app: { description: true } }>) => dictionary.app.description

export const resetPasswordExpiration = 1000 * 60 * 60 * 24 // 24 hours
export const resendResetPasswordExpiration = 1000 * 60 * 5 // 5 minutes
export const emailVerificationExpiration = 1000 * 60 * 60 * 24 * 3 // 3 days
export const resendEmailVerificationExpiration = 1000 * 60 * 2 // 5 minutes
export const defaultMaxPerPage = 100
export const maxUploadSize = 1024 * 1024 * 10 // 10 MB

export const otpWindow = 1

export const lastLocaleExpirationInSeconds = 60 * 60 * 24 * 30 // 30 days

// Free Trial Constants
export const FREE_TRIAL_REMINDER_DAYS_BEFORE = 2 // Send reminder 2 days before trial ends
export const DEFAULT_FREE_TRIAL_DAYS = 7 // Default free trial period if not specified in plan

// Account Freezing Constants
export const ACCOUNT_FREEZE_GRACE_PERIOD_DAYS = 30 // Days user can recover frozen account (null = indefinite)

// Promotional Code Constants
export const PROMOTIONAL_CODE_MIN_LENGTH = 3
export const PROMOTIONAL_CODE_MAX_LENGTH = 50
export const PROMOTIONAL_CODE_MAX_USAGE_DEFAULT = 1000 // Default max usage if not specified
export const PROMOTIONAL_CODE_MAX_DISCOUNT_PERCENTAGE = 100
export const PROMOTIONAL_CODE_MAX_PAYMENT_COUNT = 12 // Maximum number of payments a code can apply to
