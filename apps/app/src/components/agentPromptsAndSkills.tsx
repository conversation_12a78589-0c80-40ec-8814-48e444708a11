"use client"

import React, { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import CreatableSelect from "react-select/creatable"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Input, Textarea } from "@nextui-org/input"

import SkillChip from "./skill-chip"

interface AgentPromptsAndSkillsProps {
  agentId: number
}

const AgentPromptsAndSkills: React.FC<AgentPromptsAndSkillsProps> = ({ agentId }) => {
  const router = useRouter()

  // State for prompt
  const [newPromptTitle, setNewPromptTitle] = useState("")
  const [newPromptBody, setNewPromptBody] = useState("")

  const [selectedSkills, setSelectedSkills] = useState<{ value: number | string; label: string }[]>([])

  const [newlyAddedSkills, setNewlyAddedSkills] = useState<Set<string>>(new Set())

  // Fetch agent data
  const {
    data: agent,
    isLoading: isLoadingAgent,
    refetch: refetchAgent,
  } = trpc.agent.getById.useQuery(agentId, { enabled: !!agentId })

  // Fetch all skills
  const { data: allSkills, isLoading: isLoadingSkills } = trpc.skill.getAll.useQuery()

  // Mutations
  const createPrompt = trpc.prompt.create.useMutation()
  const createSkill = trpc.skill.create.useMutation()
  const updateAgent = trpc.agent.update.useMutation()
  const updatePrompt = trpc.prompt.update.useMutation()

  // Initialize selected skills when agent data is loaded
  useEffect(() => {
    if (agent) {
      if (agent.skills) {
        setSelectedSkills(
          agent.skills.map((skill) => ({
            value: skill.id,
            label: skill.name,
          }))
        )
      }

      // Si l'agent a un prompt, initialiser les champs avec ses valeurs
      if (agent.prompts && agent.prompts.length > 0) {
        const prompt = agent.prompts[0]
        setNewPromptTitle(prompt.title || "")
        setNewPromptBody(prompt.body || "")
      }
    }
  }, [agent])

  // Format skill options for react-select
  const skillOptions = allSkills ? allSkills.map((skill) => ({ value: skill.id, label: skill.name })) : []

  // Handle creating or updating prompt
  const handleSavePrompt = async () => {
    if (!newPromptTitle.trim()) {
      toast.error("Veuillez entrer un titre pour le prompt")
      return
    }

    if (!newPromptBody.trim()) {
      toast.error("Veuillez entrer un contenu pour le prompt")
      return
    }

    try {
      // Récupérer les données actuelles de l'agent
      const currentAgent = agent
      if (!currentAgent) {
        toast.error("Impossible de récupérer les données actuelles de l'agent")
        return
      }

      let promptId

      // Si l'agent a déjà un prompt, le mettre à jour
      if (currentAgent.prompts && currentAgent.prompts.length > 0) {
        const updatedPrompt = await updatePrompt.mutateAsync({
          id: currentAgent.prompts[0].id,
          title: newPromptTitle,
          body: newPromptBody,
        })
        promptId = updatedPrompt.id
      } else {
        // Sinon, créer un nouveau prompt
        const createdPrompt = await createPrompt.mutateAsync({
          title: newPromptTitle,
          body: newPromptBody,
          agentId: agentId,
        })
        promptId = createdPrompt.id
      }

      // Mettre à jour l'agent avec le prompt
      await updateAgent.mutateAsync({
        id: agentId,
        promptId: promptId,
        // Préserver les autres champs importants
        icon: currentAgent.icon,
        title: currentAgent.title,
        description: currentAgent.description,
        model: currentAgent.model,
        temperature: currentAgent.temperature,
        personality: currentAgent.personality,
        additionalInstructions: currentAgent.additionalInstructions,
        badgeId: currentAgent.badgeId!,
      })

      // Rafraîchir les données de l'agent
      await refetchAgent()

      toast.success("Prompt sauvegardé avec succès!")
    } catch (error) {
      console.error("Failed to save prompt:", error)
      toast.error("Échec de la sauvegarde du prompt, veuillez réessayer!")
    }
  }

  // Handle creating new skill
  const handleCreateSkill = async (inputValue: string) => {
    try {
      // Optimistic update with temporary ID
      const tempId = `temp-${Date.now()}`
      const tempSkill = { value: tempId, label: inputValue }
      setSelectedSkills((prev) => [...prev, tempSkill])

      // Mark as newly added
      setNewlyAddedSkills((prev) => new Set(prev).add(inputValue))

      // Create skill in database
      const newSkill = await createSkill.mutateAsync({
        name: inputValue,
      })

      // Replace temporary skill with real one
      setSelectedSkills((prev) =>
        prev.map((skill) => (skill.value === tempId ? { value: newSkill.id, label: newSkill.name } : skill))
      )
    } catch (error: any) {
      console.error("Failed to create skill:", error)

      // Display specific error message from server
      if (error.message) {
        toast.error(error.message)
      } else {
        toast.error("Échec de création de compétence. Veuillez réessayer.")
      }

      // Remove failed optimistic entry
      setSelectedSkills((prev) => prev.filter((skill) => typeof skill.value === "number"))
    }
  }

  // Save changes for skills
  const handleSaveChanges = async () => {
    try {
      // Convert skills from react-select format to array of IDs
      const skillIds = selectedSkills
        .filter((skill) => typeof skill.value === "number")
        .map((skill) => skill.value as number)

      // Récupérer les données actuelles de l'agent
      const currentAgent = agent
      if (!currentAgent) {
        toast.error("Impossible de récupérer les données actuelles de l'agent")
        return
      }

      // Mettre à jour uniquement les compétences
      await updateAgent.mutateAsync({
        id: agentId,
        skills: skillIds,
        // Préserver les autres champs importants
        icon: currentAgent.icon,
        title: currentAgent.title,
        description: currentAgent.description,
        model: currentAgent.model,
        temperature: currentAgent.temperature,
        personality: currentAgent.personality,
        additionalInstructions: currentAgent.additionalInstructions,
        badgeId: currentAgent.badgeId!,
        promptId: currentAgent.prompts && currentAgent.prompts.length > 0 ? currentAgent.prompts[0].id : undefined,
      })

      // Refetch agent data
      await refetchAgent()

      // Clear newly added skills
      setNewlyAddedSkills(new Set())

      toast.success("Compétences mises à jour avec succès!")

      // Redirection vers la vue admin de l'agent
      router.push(`/admin/agents/${agentId}/view`)
    } catch (error) {
      console.error("Failed to update agent:", error)
      toast.error("Échec de mise à jour des compétences. Veuillez réessayer.")
    }
  }

  if (isLoadingAgent) {
    return <div className="p-8 text-center">Chargement des données de l&apos;agent...</div>
  }

  return (
    <Card className="w-full shadow-md">
      <CardHeader className="flex gap-3">
        <div className="flex flex-col">
          <p className="text-xl font-bold">Configurer l&apos;Agent</p>
          <p className="text-small text-default-500">Ajoutez un prompt et des compétences à votre agent</p>
        </div>
      </CardHeader>
      <CardBody className="space-y-6">
        {/* Prompt Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Prompt de l&apos;Agent</h3>
          <p className="text-sm">Définissez le prompt qui déterminera le comportement de votre agent.</p>

          <div className="space-y-4 rounded-md">
            <Input
              label="Titre du Prompt"
              value={newPromptTitle}
              onChange={(e) => setNewPromptTitle(e.target.value)}
              placeholder="Entrez un titre descriptif"
              isRequired
            />

            <Textarea
              label="Contenu du Prompt"
              value={newPromptBody}
              onChange={(e) => setNewPromptBody(e.target.value)}
              placeholder="Entrez le contenu du prompt..."
              minRows={5}
              isRequired
            />

            <Button color="primary" onPress={handleSavePrompt}>
              {agent?.prompts && agent.prompts.length > 0 ? "Mettre à jour le Prompt" : "Créer le Prompt"}
            </Button>
          </div>
        </div>

        {/* Skills Management Section */}
        <div className="space-y-4 pt-4">
          <h3 className="text-lg font-semibold">Compétences de l&apos;Agent</h3>
          <p className="text-sm text-gray-600">
            Sélectionnez des compétences existantes ou créez-en de nouvelles pour définir ce que votre agent peut faire.
          </p>

          {/* Skills Selection with react-select/creatable */}
          <div className="w-full space-y-2">
            <label className="block text-sm font-medium">Skills</label>
            <CreatableSelect
              isMulti
              isClearable
              isLoading={isLoadingSkills}
              options={skillOptions}
              value={selectedSkills}
              // @ts-expect-error Dispatch and useState funcs conflicting
              onChange={setSelectedSkills}
              onCreateOption={handleCreateSkill}
              placeholder="Sélectionnez ou créez des compétences..."
              formatCreateLabel={(inputValue) => `Créer la compétence "${inputValue}"`}
              className="w-full"
              classNamePrefix="react-select"
              components={{
                MultiValueContainer: () => null,
                MultiValueRemove: () => null,
              }}
              menuPortalTarget={typeof document !== "undefined" ? document.body : undefined}
              styles={{
                menuPortal: (base) => ({
                  ...base,
                  zIndex: 9999,
                }),
              }}
            />

            {/* Display selected skills as custom chips */}
            {selectedSkills.length > 0 && (
              <div className="mt-4 flex flex-wrap gap-2">
                {selectedSkills.map((skill) => (
                  <SkillChip
                    key={skill.value}
                    label={skill.label}
                    onRemove={() => {
                      setSelectedSkills(selectedSkills.filter((s) => s.value !== skill.value))
                    }}
                    isNew={newlyAddedSkills.has(skill.label)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Save Changes Button */}
        <Button onClick={handleSaveChanges} className="w-full" variant="flat" color="primary" size="lg">
          Enregistrer la configuration
        </Button>
      </CardBody>
    </Card>
  )
}

export default AgentPromptsAndSkills
