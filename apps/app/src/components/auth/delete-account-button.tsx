"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { toast } from "react-toastify"

import { authRoutes } from "@/constants/auth"
import { TDictionary } from "@/lib/langs"
import { trpc } from "@/lib/trpc/client"
import { But<PERSON> } from "@nextui-org/button"
import { <PERSON><PERSON>, <PERSON>dal<PERSON>ontent, ModalFooter } from "@nextui-org/modal"

import { ModalDescription, ModalHeader, ModalTitle } from "../ui/modal"

import { DeleteAccountButtonDr } from "./delete-account-button.dr"

export default function DeleteAccountButton({
  children,
  dictionary,
}: {
  children: React.ReactNode
  dictionary: TDictionary<typeof DeleteAccountButtonDr>
}) {
  const router = useRouter()
  const deleteAccountMutation = trpc.me.deleteAccount.useMutation({
    onSuccess: async () => {
      toast.success(dictionary.deleteAccountSuccessDescription)
      // Sign out the user after successful account deletion
      const { signOut } = await import("next-auth/react")
      await signOut({ callbackUrl: authRoutes.signUp[0], redirect: false })
      router.push(authRoutes.signUp[0])
    },
  })

  const handleDeleteAccount = () => {
    deleteAccountMutation.mutate()
  }

  const [showModal, setShowModal] = useState(false)

  return (
    <>
      <Button color="danger" isLoading={deleteAccountMutation.isPending} onPress={() => setShowModal(true)}>
        {children}
      </Button>
      <Modal isOpen={showModal} onOpenChange={(open) => setShowModal(open)}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader>
                <ModalTitle>{dictionary.deleteAccountConfirmationTitle}</ModalTitle>
                <ModalDescription>{dictionary.deleteAccountConfirmationDescription}</ModalDescription>
              </ModalHeader>
              <ModalFooter>
                <Button onPress={onClose} variant="flat">
                  {dictionary.cancel}
                </Button>
                <Button color="danger" onPress={handleDeleteAccount} isLoading={deleteAccountMutation.isPending}>
                  {dictionary.deleteAccountConfirm}
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  )
}
