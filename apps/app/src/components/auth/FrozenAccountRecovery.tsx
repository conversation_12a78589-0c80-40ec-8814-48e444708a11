"use client"

import { useState } from "react"
import { Alert<PERSON>riangle, RefreshCw } from "lucide-react"
import { toast } from "sonner"

import { trpc } from "@/lib/trpc/client"
import { Button } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"

interface FrozenAccountRecoveryProps {
  userId: string
  email: string
  onRecoverySuccess: () => void
}

export function FrozenAccountRecovery({ userId, email, onRecoverySuccess }: FrozenAccountRecoveryProps) {
  const [isRecovering, setIsRecovering] = useState(false)

  const recoverAccountMutation = trpc.me.recoverFrozenAccount.useMutation({
    onSuccess: (result) => {
      toast.success(result.message || "Compte récupéré avec succès")
      onRecoverySuccess()
    },
    onError: (error) => {
      toast.error(error.message || "Erreur lors de la récupération du compte")
      setIsRecovering(false)
    },
  })

  const handleRecover = async () => {
    setIsRecovering(true)
    try {
      await recoverAccountMutation.mutateAsync({ userId })
    } catch (error) {
      // Error handled by mutation
    }
  }

  return (
    <Card className="mx-auto w-full max-w-md">
      <CardHeader className="flex flex-col items-center gap-2 pb-4">
        <AlertTriangle className="size-8 text-warning" />
        <h2 className="text-center text-xl font-semibold">Récupération de compte</h2>
      </CardHeader>
      <CardBody className="space-y-4">
        <div className="space-y-2 text-center">
          <p className="text-sm text-default-600">Votre compte ({email}) a été temporairement suspendu.</p>
          <p className="text-sm text-default-600">
            Vous pouvez le récupérer maintenant et retrouver immédiatement l&apos;accès à tous vos services.
          </p>
        </div>

        <div className="flex flex-col gap-3">
          <Button
            color="primary"
            size="lg"
            onPress={handleRecover}
            isLoading={isRecovering}
            startContent={!isRecovering && <RefreshCw className="size-4" />}
            className="w-full"
          >
            {isRecovering ? "Récupération en cours..." : "Récupérer mon compte"}
          </Button>

          <p className="text-center text-xs text-default-500">
            Cette option est disponible pendant une période limitée.
          </p>
        </div>
      </CardBody>
    </Card>
  )
}
