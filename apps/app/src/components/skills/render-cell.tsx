"use client"

import React, { useState } from "react"
import { Pen<PERSON><PERSON>, Trash } from "lucide-react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { logger } from "@coheadcoaching/lib"
import { Button } from "@nextui-org/button"
import { Input } from "@nextui-org/input"
import { <PERSON><PERSON>, ModalBody, ModalContent, Modal<PERSON>ooter, ModalHeader } from "@nextui-org/modal"
import { Tooltip } from "@nextui-org/tooltip"
import { Prisma } from "@prisma/client"

const isRenderable = (child: unknown) => {
  return React.isValidElement(child) || typeof child === "string" || typeof child === "number"
}
type SkillType = Prisma.SkillGetPayload<{
  include: {
    _count: {
      select: {
        agents: true
      }
    }
  }
}>

interface Props {
  skill: SkillType
  columnKey: string | React.Key
  onMutation?: () => void
}

export const RenderCell = ({ skill, columnKey, onMutation }: Props) => {
  //@ts-expect-error Any type detected
  const cellValue = skill[columnKey]
  switch (columnKey) {
    case "agentsCount":
      return skill._count.agents
    case "actions":
      return (
        <div className="flex items-center gap-4">
          <EditSkillModal skill={skill} onSkillUpdated={onMutation} />
          <DeleteSkillModal skill={skill} onSkillDeleted={onMutation} />
        </div>
      )
    default:
      return isRenderable(cellValue) ? cellValue : "<___>"
  }
}

/****
 ** Modals down here
 */

interface EditSkillModalProps {
  skill: SkillType
  onSkillUpdated?: () => void
}

const EditSkillModal = ({ skill, onSkillUpdated }: EditSkillModalProps) => {
  const utils = trpc.useUtils()
  const [showModal, setShowModal] = useState(false)
  const [formData, setFormData] = useState({
    id: skill.id,
    name: skill.name,
  })

  const skillUpdate = trpc.skill.update.useMutation({
    onSuccess: () => {
      setShowModal(false)
      toast.success("Skill mis à jour avec succès!")
      utils.user.getAll.invalidate()
      if (onSkillUpdated) {
        onSkillUpdated()
      }
    },
    onError: (error) => {
      logger.log(error)
      // Display specific error message from server
      if (error.message) {
        toast.error(error.message)
      } else {
        toast.error("Erreur lors de la mise à jour de la compétence")
      }
    },
  })

  const handleOpenModal = () => {
    setShowModal(true)
  }

  const handleCloseModal = () => {
    setShowModal(false)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = () => {
    skillUpdate.mutate(formData)
  }

  return (
    <>
      <Tooltip content="Modifier" color="secondary">
        <Button
          isIconOnly
          variant="light"
          size="sm"
          radius="full"
          onPress={handleOpenModal}
          className="text-default-500 transition-colors hover:text-secondary"
        >
          <Pencil size={18} />
        </Button>
      </Tooltip>

      <Modal
        isOpen={showModal}
        onClose={handleCloseModal}
        scrollBehavior="inside"
        size="md"
        placement="center"
        backdrop="blur"
        classNames={{
          body: "py-6",
          closeButton: "hover:bg-default-100",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-xl font-bold">Modifier skill</h2>
            <p className="text-sm text-default-500">Mise à jour des informations du skill</p>
          </ModalHeader>
          <ModalBody>
            <form className="flex">
              <Input
                label="Nom complet"
                name="name"
                value={formData.name || ""}
                onChange={handleInputChange}
                placeholder="Saisissez le nom complet"
                size="lg"
                variant="bordered"
                labelPlacement="outside"
                classNames={{
                  label: "text-sm font-medium text-default-700 dark:text-default-500",
                }}
              />
            </form>
          </ModalBody>
          <ModalFooter>
            <Button onPress={handleCloseModal} variant="light" color="danger">
              Annuler
            </Button>
            <Button isLoading={skillUpdate.isPending} onPress={handleSubmit} color="secondary" variant="flat">
              Enregistrer les modifications
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}

interface DeleteSkillModalProps {
  skill: SkillType
  onSkillDeleted?: () => void
}

const DeleteSkillModal = ({ skill, onSkillDeleted }: DeleteSkillModalProps) => {
  const utils = trpc.useUtils()
  const [showModal, setShowModal] = useState(false)

  const skillDelete = trpc.skill.delete.useMutation({
    onSuccess: () => {
      setShowModal(false)
      toast.success("Skill supprimé avec succès!")
      utils.skill.getAll.invalidate()
      if (onSkillDeleted) {
        onSkillDeleted()
      }
    },
    onError: (error) => {
      logger.log(error)
      toast.error("Erreur lors de la suppression du skill")
    },
  })

  const handleOpenModal = () => {
    if (skill._count.agents > 0) {
      toast.error("Vous ne pouvez pas vous supprimer quand des agents sont associés!")
      return
    }
    setShowModal(true)
  }

  const handleCloseModal = () => {
    setShowModal(false)
  }

  const handleSkillDelete = () => {
    skillDelete.mutate(skill.id)
  }

  return (
    <>
      <Tooltip content="Supprimer" color="danger">
        <Button
          isIconOnly
          variant="light"
          size="sm"
          radius="full"
          onPress={handleOpenModal}
          className="text-default-500 transition-colors hover:text-danger"
        >
          <Trash size={18} />
        </Button>
      </Tooltip>

      <Modal
        isOpen={showModal}
        onClose={handleCloseModal}
        size="md"
        placement="center"
        backdrop="blur"
        classNames={{
          body: "py-6",
          closeButton: "hover:bg-default-100",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-xl font-bold text-danger">Confirmation de suppression</h2>
            <p className="text-sm text-default-500">Cette action ne peut pas être annulée</p>
          </ModalHeader>
          <ModalBody>
            <div className="rounded-lg bg-danger-50 p-4">
              <p className="text-sm text-danger">
                Vous êtes sur le point de supprimer définitivement cet utilisateur. Cette action supprimera toutes les
                données associées à ce compte et ne peut pas être annulée.
              </p>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button onPress={handleCloseModal} variant="light">
              Annuler
            </Button>
            <Button isLoading={skillDelete.isPending} onPress={handleSkillDelete} color="danger" variant="flat">
              Supprimer définitivement
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}
