import React, { useEffect, useLayoutEffect, useMemo, useRef, useState } from "react"

import { Input } from "@nextui-org/input"
import { Select, SelectItem } from "@nextui-org/select"

type Option = { label: string; key: string; name?: string }

interface SelectWithSearchProps {
  options?: Option[]
  label?: string
  value: string
  onChange: (value: string) => void
  placeholder?: string
  errorMessage?: string
  isInvalid?: boolean
  isRequired?: boolean
  radius?: "sm" | "md" | "lg"
  classNames?: {
    base?: string
    trigger?: string
    popoverContent?: string
    selectorIcon?: string
    input?: string
  }
  // Backend search props
  onSearch?: (query: string) => void
  searchResults?: Option[]
  isSearching?: boolean
  noResultsMessage?: string
  searchPlaceholder?: string
  debounceMs?: number
  minSearchLength?: number
}

const SelectWithSearch: React.FC<SelectWithSearchProps> = ({
  options = [],
  label,
  value,
  onChange,
  placeholder,
  errorMessage,
  isInvalid,
  isRequired,
  classNames,
  radius = "sm",
  // Backend search props
  onSearch,
  searchResults,
  isSearching = false,
  noResultsMessage = "Aucun résultat trouvé",
  searchPlaceholder = "Rechercher...",
  debounceMs = 300,
  minSearchLength = 2,
}) => {
  const [search, setSearch] = useState("")
  const searchInputRef = useRef<HTMLInputElement>(null)

  // Use a custom debounce hook
  const useDebounce = (value: string, delay: number) => {
    const [debouncedValue, setDebouncedValue] = useState(value)

    useEffect(() => {
      const handler = setTimeout(() => {
        setDebouncedValue(value)
      }, delay)

      return () => {
        clearTimeout(handler)
      }
    }, [value, delay])

    return debouncedValue
  }

  const debouncedSearch = useDebounce(search, debounceMs)

  // Trigger backend search when debounced search changes
  useEffect(() => {
    if (onSearch && debouncedSearch.length >= minSearchLength) {
      onSearch(debouncedSearch)
    }
  }, [debouncedSearch, onSearch, minSearchLength])

  // Determine which options to display
  const displayOptions = useMemo(() => {
    if (onSearch) {
      // Backend search mode
      return searchResults || []
    } else {
      // Client-side filtering mode
      return options.filter(
        (opt) =>
          opt.label.toLowerCase().includes(search.toLowerCase()) ||
          opt.key.includes(search) ||
          (opt.name && opt.name.toLowerCase().includes(search.toLowerCase()))
      )
    }
  }, [onSearch, searchResults, options, search])

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange(e.target.value)
    setSearch("")
  }

  useLayoutEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }, [search])

  // Show loading or no results message
  const showNoResults = !isSearching && displayOptions.length === 0 && search.length >= minSearchLength
  const showMinLength = onSearch && search.length > 0 && search.length < minSearchLength

  return (
    <Select
      label={label}
      radius={radius}
      selectedKeys={[value]}
      onChange={handleChange}
      placeholder={placeholder}
      errorMessage={errorMessage}
      isInvalid={isInvalid}
      isRequired={isRequired}
      classNames={classNames}
      listboxProps={{
        topContent: (
          <div role="search">
            <Input
              ref={searchInputRef}
              type="text"
              placeholder={searchPlaceholder}
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              aria-label="Search"
              tabIndex={0}
            />
            {isSearching && <div className="mt-2 text-center text-xs text-default-500">Recherche en cours...</div>}
            {showMinLength && (
              <div className="mt-2 text-center text-xs text-default-500">
                Tapez au moins {minSearchLength} caractères
              </div>
            )}
            {showNoResults && <div className="mt-2 text-center text-xs text-default-500">{noResultsMessage}</div>}
          </div>
        ),
      }}
    >
      {displayOptions.map((opt) => (
        <SelectItem key={opt.key} textValue={opt.label}>
          {opt.label}
        </SelectItem>
      ))}
    </Select>
  )
}

export default SelectWithSearch
