"use client"

import React, { useState } from "react"
import { useSession } from "next-auth/react"
import { <PERSON>, Pencil, Trash } from "lucide-react"
import { toast } from "react-toastify"

import { roleDescriptions, roleLabels } from "@/constants"
import { trpc } from "@/lib/trpc/client"
import { getImageUrl } from "@/lib/utils/client-utils"
import { logger } from "@coheadcoaching/lib"
import { Button } from "@nextui-org/button"
import { Card, CardBody } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { Divider } from "@nextui-org/divider"
import { Input } from "@nextui-org/input"
import { Modal, ModalBody, ModalContent, ModalFooter, ModalHeader } from "@nextui-org/modal"
import { Select, SelectItem } from "@nextui-org/select"
import { SharedSelection } from "@nextui-org/system"
import { Tooltip } from "@nextui-org/tooltip"
import { User } from "@nextui-org/user"
import { <PERSON>risma, User<PERSON><PERSON> } from "@prisma/client"

const isRenderable = (child: unknown) => {
  return React.isValidElement(child) || typeof child === "string" || typeof child === "number"
}

type UserType = Prisma.UserGetPayload<{ include: { profilePicture: true } }>

interface Props {
  user: UserType
  columnKey: string | React.Key
  onMutation?: () => void
}

export const RenderCell = ({ user, columnKey, onMutation }: Props) => {
  //@ts-expect-error Any type detected
  const cellValue = user[columnKey]
  switch (columnKey) {
    case "name":
      return (
        <User
          avatarProps={{
            src: getImageUrl(user.profilePicture) || user.image || undefined,
            size: "sm",
            radius: "full",
            className: "border-2 border-neutral-200 dark:border-neutral-700",
          }}
          name={cellValue || "<___>"}
          classNames={{
            name: "text-foreground",
            description: "text-foreground-500",
          }}
        >
          {user.email}
        </User>
      )
    case "role":
      return (
        <div className="flex flex-wrap gap-1">
          {user.roles && user.roles.length > 0 ? (
            user.roles.map((role) => (
              <Tooltip key={role} content={roleDescriptions[role as keyof typeof roleDescriptions]}>
                <Chip
                  size="sm"
                  variant="flat"
                  color={
                    role === "ADMIN"
                      ? "primary"
                      : role === "SAV"
                        ? "success"
                        : role === "MODO"
                          ? "secondary"
                          : role === "IA_BUILDER"
                            ? "warning"
                            : "default"
                  }
                  classNames={{
                    base: "text-xs font-medium",
                  }}
                >
                  {roleLabels[role as keyof typeof roleLabels] || role}
                </Chip>
              </Tooltip>
            ))
          ) : (
            <Chip
              size="sm"
              variant="flat"
              color={user.role === "ADMIN" ? "primary" : "default"}
              classNames={{
                base: "text-xs font-medium",
              }}
            >
              {user.role}
            </Chip>
          )}
        </div>
      )
    case "emailVerified":
      return (
        <Chip
          size="sm"
          variant="flat"
          color={user.emailVerified ? "success" : "danger"}
          classNames={{
            base: "text-xs font-medium",
          }}
        >
          {user.emailVerified ? "Vérifié" : "Non vérifié"}
        </Chip>
      )
    case "accountStatus":
      return (
        <Chip
          size="sm"
          variant="flat"
          color={!user.isFrozen ? "success" : "danger"}
          classNames={{
            base: "text-xs font-medium",
          }}
        >
          {!user.isFrozen ? "Actif" : "Inactif"}
        </Chip>
      )
    case "actions":
      return (
        <div className="flex items-center gap-4">
          <ViewUserModal user={user} />
          <EditUserModal user={user} onUserUpdated={onMutation} />
          <DeleteUserModal user={user} onUserDeleted={onMutation} />
        </div>
      )
    default:
      return isRenderable(cellValue) ? cellValue : "<___>"
  }
}

/****
 ** Modals down here
 */

interface ViewUserModalProps {
  user: UserType
}

const ViewUserModal = ({ user }: ViewUserModalProps) => {
  const [showModal, setShowModal] = useState(false)

  const handleOpenModal = () => {
    setShowModal(true)
  }

  const handleCloseModal = () => {
    setShowModal(false)
  }

  return (
    <>
      <Tooltip content="Détails" color="primary">
        <Button
          isIconOnly
          variant="light"
          size="sm"
          radius="full"
          onPress={handleOpenModal}
          className="text-default-500 transition-colors hover:text-primary"
        >
          <Eye size={18} />
        </Button>
      </Tooltip>

      <Modal
        isOpen={showModal}
        onClose={handleCloseModal}
        scrollBehavior="inside"
        size="3xl"
        placement="center"
        backdrop="blur"
        classNames={{
          body: "py-6",
          closeButton: "hover:bg-default-100",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-xl font-bold">Détails de l&apos;utilisateur</h2>
            <p className="text-sm text-default-500">Informations complètes du profil</p>
          </ModalHeader>
          <ModalBody>
            <div className="flex flex-col gap-6">
              <div className="flex items-center gap-4">
                <User
                  avatarProps={{
                    src: user.image || undefined,
                    size: "lg",
                    radius: "full",
                    className: "border-2 border-neutral-200 dark:border-neutral-700",
                  }}
                  name={user.name || "<Non défini>"}
                  description={user.email}
                  classNames={{
                    name: "text-lg font-semibold",
                    description: "text-default-500",
                  }}
                />
                <div className="ml-auto flex flex-wrap justify-end gap-1">
                  {user.roles && user.roles.length > 0 ? (
                    user.roles.map((role) => (
                      <Tooltip key={role} content={roleDescriptions[role as keyof typeof roleDescriptions]}>
                        <Chip
                          size="md"
                          variant="flat"
                          color={
                            role === "ADMIN"
                              ? "primary"
                              : role === "SAV"
                                ? "success"
                                : role === "MODO"
                                  ? "secondary"
                                  : role === "IA_BUILDER"
                                    ? "warning"
                                    : "default"
                          }
                        >
                          {roleLabels[role as keyof typeof roleLabels] || role}
                        </Chip>
                      </Tooltip>
                    ))
                  ) : (
                    <Chip size="md" variant="flat" color={user.role === "ADMIN" ? "primary" : "default"}>
                      {user.role}
                    </Chip>
                  )}
                </div>
              </div>

              <Divider className="my-2" />

              <div className="grid grid-cols-1 gap-x-8 gap-y-6 md:grid-cols-2">
                <Card className="border border-default-200 dark:border-default-100/20">
                  <CardBody className="p-4">
                    <div className="flex flex-col gap-1">
                      <span className="text-xs uppercase text-default-500">Identifiant</span>
                      <span className="break-all font-mono text-sm">{user.id}</span>
                    </div>
                  </CardBody>
                </Card>

                <Card className="border border-default-200 dark:border-default-100/20">
                  <CardBody className="p-4">
                    <div className="flex flex-col gap-1">
                      <span className="text-xs uppercase text-default-500">Email</span>
                      <span className="text-sm">{user.email}</span>
                    </div>
                  </CardBody>
                </Card>

                <Card className="border border-default-200 dark:border-default-100/20">
                  <CardBody className="p-4">
                    <div className="flex flex-col gap-1">
                      <span className="text-xs uppercase text-default-500">Nom d&apos;utilisateur</span>
                      <span className="text-sm">{user.username || "<Non défini>"}</span>
                    </div>
                  </CardBody>
                </Card>

                <Card className="border border-default-200 dark:border-default-100/20">
                  <CardBody className="p-4">
                    <div className="flex flex-col gap-1">
                      <span className="text-xs uppercase text-default-500">Statut du compte</span>
                      <div className="flex items-center gap-2">
                        <Chip size="sm" variant="flat" color={user.emailVerified ? "success" : "danger"}>
                          {user.emailVerified ? "Email vérifié" : "Email non vérifié"}
                        </Chip>
                      </div>
                    </div>
                  </CardBody>
                </Card>

                <Card className="border border-default-200 dark:border-default-100/20">
                  <CardBody className="p-4">
                    <div className="flex flex-col gap-1">
                      <span className="text-xs uppercase text-default-500">Authentification</span>
                      <div className="flex items-center gap-2">
                        <Chip size="sm" variant="flat" color={user.hasPassword ? "success" : "warning"}>
                          {user.hasPassword ? "Mot de passe configuré" : "Pas de mot de passe"}
                        </Chip>
                        <Chip size="sm" variant="flat" color={user.otpVerified ? "success" : "default"}>
                          {user.otpVerified ? "2FA activé" : "2FA inactif"}
                        </Chip>
                      </div>
                    </div>
                  </CardBody>
                </Card>

                <Card className="border border-default-200 dark:border-default-100/20">
                  <CardBody className="p-4">
                    <div className="flex flex-col gap-1">
                      <span className="text-xs uppercase text-default-500">Préférences</span>
                      <div className="flex items-center gap-2">
                        <Chip size="sm" variant="flat" color="default">
                          Langue: {user.lastLocale || "Default"}
                        </Chip>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </div>

              <Divider className="my-2" />

              <div className="flex items-center justify-between text-xs text-default-500">
                <div>
                  Créé le {new Date(user.createdAt).toLocaleDateString()} à{" "}
                  {new Date(user.createdAt).toLocaleTimeString()}
                </div>
                <div>
                  Dernière mise à jour le {new Date(user.updatedAt).toLocaleDateString()} à{" "}
                  {new Date(user.updatedAt).toLocaleTimeString()}
                </div>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button onPress={handleCloseModal} color="primary" variant="light">
              Fermer
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}

interface EditUserModalProps {
  user: UserType
  onUserUpdated?: () => void
}

const EditUserModal = ({ user, onUserUpdated }: EditUserModalProps) => {
  const utils = trpc.useUtils()
  const [showModal, setShowModal] = useState(false)
  const [formData, setFormData] = useState({
    id: user.id,
    name: user.name,
    email: user.email,
    username: user.username,
    role: user.role,
    roles: user.roles || [user.role], // Utiliser les rôles existants ou le rôle legacy
  })

  const userUpdate = trpc.user.update.useMutation({
    onSuccess: () => {
      setShowModal(false)
      toast.success("Utilisateur mis à jour avec succès!")
      utils.user.getAll.invalidate()
      if (onUserUpdated) {
        onUserUpdated()
      }
    },
    onError: (error) => {
      logger.log(error)
      toast.error("Erreur lors de la mise à jour de l'utilisateur")
    },
  })

  const handleOpenModal = () => {
    setShowModal(true)
  }

  const handleCloseModal = () => {
    setShowModal(false)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  // Handler for role multi-selection
  const handleRolesChange = (keys: SharedSelection) => {
    // Convert Set to Array and ensure USER is always included
    const selectedRoles = Array.from(keys) as unknown as UserRole[]
    if (!selectedRoles.includes("USER")) {
      selectedRoles.push("USER")
    }

    // Update form data
    setFormData((prev) => ({
      ...prev,
      roles: selectedRoles,
      // If ADMIN is selected, also set the legacy role field to ADMIN
      role: selectedRoles.includes("ADMIN") ? "ADMIN" : "USER",
    }))
  }

  const handleSubmit = () => {
    userUpdate.mutate(formData)
  }

  return (
    <>
      <Tooltip content="Modifier" color="secondary">
        <Button
          isIconOnly
          variant="light"
          size="sm"
          radius="full"
          onPress={handleOpenModal}
          className="text-default-500 transition-colors hover:text-secondary"
        >
          <Pencil size={18} />
        </Button>
      </Tooltip>

      <Modal
        isOpen={showModal}
        onClose={handleCloseModal}
        scrollBehavior="inside"
        size="2xl"
        placement="center"
        backdrop="blur"
        classNames={{
          body: "py-6",
          closeButton: "hover:bg-default-100",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-xl font-bold">Modifier l&apos;utilisateur</h2>
            <p className="text-sm text-default-500">Mise à jour des informations du profil</p>
          </ModalHeader>
          <ModalBody>
            <form className="flex flex-col gap-6">
              <div className="mb-2 flex items-center gap-4">
                <User
                  avatarProps={{
                    src: user.image || undefined,
                    size: "md",
                    radius: "full",
                    className: "border-2 border-neutral-200 dark:border-neutral-700",
                  }}
                  name={user.name || user.username || "<Non défini>"}
                  description={user.email}
                  classNames={{
                    name: "text-foreground",
                    description: "text-default-500",
                  }}
                />
              </div>

              <Divider className="my-2" />

              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <Input
                  label="Nom complet"
                  name="name"
                  value={formData.name || ""}
                  onChange={handleInputChange}
                  placeholder="Saisissez le nom complet"
                  size="lg"
                  variant="bordered"
                  labelPlacement="outside"
                  classNames={{
                    label: "text-sm font-medium text-default-700 dark:text-default-500",
                  }}
                />

                <Input
                  label="Nom d'utilisateur"
                  name="username"
                  value={formData.username || ""}
                  onChange={handleInputChange}
                  placeholder="Saisissez le nom d'utilisateur"
                  size="lg"
                  variant="bordered"
                  labelPlacement="outside"
                  classNames={{
                    label: "text-sm font-medium text-default-700 dark:text-default-500",
                  }}
                />

                <Input
                  label="Email"
                  name="email"
                  type="email"
                  value={formData.email || ""}
                  onChange={handleInputChange}
                  placeholder="Saisissez l'adresse email"
                  size="lg"
                  variant="bordered"
                  labelPlacement="outside"
                  classNames={{
                    label: "text-sm font-medium text-default-700 dark:text-default-500",
                  }}
                />

                <Select
                  label="Rôles de l'utilisateur"
                  selectionMode="multiple"
                  placeholder="Sélectionnez un ou plusieurs rôles"
                  description="L'utilisateur peut avoir plusieurs rôles simultanément"
                  selectedKeys={formData.roles}
                  onSelectionChange={handleRolesChange}
                  variant="bordered"
                  labelPlacement="outside"
                  classNames={{
                    label: "text-sm font-medium text-default-700 dark:text-default-500",
                    trigger: "min-h-12",
                  }}
                  renderValue={(items) => {
                    return (
                      <div className="flex flex-wrap gap-1">
                        {items.map((item) => (
                          <Chip key={item.key} size="sm" color="primary">
                            {roleLabels[item.key as keyof typeof roleLabels]}
                          </Chip>
                        ))}
                      </div>
                    )
                  }}
                >
                  {Object.entries(roleLabels).map(([role, label]) => (
                    <SelectItem
                      key={role}
                      value={role}
                      isDisabled={role === "USER"} // USER role is always selected
                      description={roleDescriptions[role as keyof typeof roleDescriptions]}
                    >
                      {label}
                    </SelectItem>
                  ))}
                </Select>
              </div>
            </form>
          </ModalBody>
          <ModalFooter>
            <Button onPress={handleCloseModal} variant="light" color="danger">
              Annuler
            </Button>
            <Button isLoading={userUpdate.isPending} onPress={handleSubmit} color="secondary" variant="flat">
              Enregistrer les modifications
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}

interface DeleteUserModalProps {
  user: UserType
  onUserDeleted?: () => void
}

const DeleteUserModal = ({ user, onUserDeleted }: DeleteUserModalProps) => {
  const session = useSession()
  const workingUser = session.data?.user as UserType

  const utils = trpc.useUtils()
  const [showModal, setShowModal] = useState(false)

  const userDelete = trpc.user.delete.useMutation({
    onSuccess: () => {
      setShowModal(false)
      toast.success("Utilisateur supprimé avec succès!")
      utils.user.getAll.invalidate()
      if (onUserDeleted) {
        onUserDeleted()
      }
    },
    onError: (error) => {
      logger.log(error)
      toast.error("Erreur lors de la suppression de l'utilisateur")
    },
  })

  const handleOpenModal = () => {
    if (user.id === workingUser?.id) {
      toast.error("Vous ne pouvez pas vous supprimer de cette manière!")
      return
    }
    setShowModal(true)
  }

  const handleCloseModal = () => {
    setShowModal(false)
  }

  const handleUserDelete = () => {
    userDelete.mutate(user.id)
  }

  return (
    <>
      <Tooltip content="Supprimer" color="danger">
        <Button
          isIconOnly
          variant="light"
          size="sm"
          radius="full"
          onPress={handleOpenModal}
          className="text-default-500 transition-colors hover:text-danger"
        >
          <Trash size={18} />
        </Button>
      </Tooltip>

      <Modal
        isOpen={showModal}
        onClose={handleCloseModal}
        size="md"
        placement="center"
        backdrop="blur"
        classNames={{
          body: "py-6",
          closeButton: "hover:bg-default-100",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-xl font-bold text-danger">Confirmation de suppression</h2>
            <p className="text-sm text-default-500">Cette action ne peut pas être annulée</p>
          </ModalHeader>
          <ModalBody>
            <div className="flex flex-col gap-4">
              <User
                avatarProps={{
                  src: user.image || undefined,
                  size: "md",
                  radius: "full",
                  className: "border-2 border-neutral-200 dark:border-neutral-700",
                }}
                name={user.name || user.username || "<Non défini>"}
                description={user.email}
                classNames={{
                  name: "text-foreground",
                  description: "text-default-500",
                }}
              />

              <Divider className="my-2" />

              <div className="rounded-lg bg-danger-50 p-4">
                <p className="text-sm text-danger">
                  Vous êtes sur le point de supprimer définitivement cet utilisateur. Cette action supprimera toutes les
                  données associées à ce compte et ne peut pas être annulée.
                </p>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button onPress={handleCloseModal} variant="light">
              Annuler
            </Button>
            <Button isLoading={userDelete.isPending} onPress={handleUserDelete} color="danger" variant="flat">
              Supprimer définitivement
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}
