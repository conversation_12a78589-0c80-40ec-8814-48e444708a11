"use client"
import React, { use<PERSON><PERSON>back, useEffect, useState } from "react"
import { DownloadIcon, Search, Trash2 } from "lucide-react"
import { toast } from "sonner"
import { utils as XLSXUtils, writeFile as XLSXWriteFile } from "xlsx"

import { trpc } from "@/lib/trpc/client"
import { Button } from "@nextui-org/button"
import { Input } from "@nextui-org/input"
import { Modal, ModalBody, ModalContent, ModalFooter, ModalHeader } from "@nextui-org/modal"
import { Pagination } from "@nextui-org/pagination"
import { Select, SelectItem } from "@nextui-org/select"
import { Spinner } from "@nextui-org/spinner"
import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@nextui-org/table"
import { Prisma } from "@prisma/client"

import CreateUserModal from "../admin/createUserModal"

import { RenderCell } from "./render-cell"
import { usersColumns } from "./users-colums"

type User = Prisma.UserGetPayload<{ include: { profilePicture: true } }>

type PaginationInfo = {
  page: number
  pageSize: number
  totalCount: number
  totalPages: number
}

interface UsersTableProps {
  initialUsers: User[]
  initialPagination: PaginationInfo
}

export const UsersTable = ({ initialUsers, initialPagination }: UsersTableProps) => {
  const [users, setUsers] = useState<User[]>(initialUsers)
  const [page, setPage] = useState(initialPagination.page)
  const [pageSize, setPageSize] = useState(initialPagination.pageSize)
  const [totalPages, setTotalPages] = useState(initialPagination.totalPages)
  const [filterValue, setFilterValue] = useState("")
  const [filterColumn, setFilterColumn] = useState("name")
  const [isLoading, setIsLoading] = useState(false)
  const [prevPage, setPrevPage] = useState(initialPagination.page)
  const [prevPageSize, setPrevPageSize] = useState(initialPagination.pageSize)
  const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set())
  const [showBulkDeleteModal, setShowBulkDeleteModal] = useState(false)

  const usersQuery = trpc.user.getAll.useQuery({ page, pageSize }, { enabled: false })

  const bulkDeleteMutation = trpc.user.bulkDelete.useMutation({
    onSuccess: (data) => {
      toast.success(`${data.deletedCount} utilisateur(s) supprimé(s) avec succès!`)
      setSelectedUsers(new Set())
      setShowBulkDeleteModal(false)
      handleUsersMutated()
    },
    onError: (error) => {
      toast.error(`Erreur lors de la suppression: ${error.message}`)
    },
  })

  const loadData = useCallback(async () => {
    setIsLoading(true)
    try {
      const result = await usersQuery.refetch()
      if (result.data) {
        setUsers(result.data.data)
        setTotalPages(result.data.pagination.totalPages)
      }
    } finally {
      setIsLoading(false)
    }
  }, [usersQuery])

  const handleUsersMutated = useCallback(async () => {
    await loadData()
  }, [loadData])

  useEffect(() => {
    const pageChanged = page !== prevPage
    const pageSizeChanged = pageSize !== prevPageSize

    if (pageChanged || pageSizeChanged) {
      loadData()
      setPrevPage(page)
      setPrevPageSize(pageSize)
    }
  }, [page, pageSize, prevPage, prevPageSize, loadData])

  // Filtrage des utilisateurs
  const filteredUsers = React.useMemo(() => {
    if (!filterValue) return users

    return users.filter((user) => {
      switch (filterColumn) {
        case "name":
          return user.name?.toLowerCase().includes(filterValue.toLowerCase())
        case "username":
          return user.username?.toLowerCase().includes(filterValue.toLowerCase())
        case "email":
          return user.email?.toLowerCase().includes(filterValue.toLowerCase())
        case "role":
          return user.role.toLowerCase().includes(filterValue.toLowerCase())
        default:
          return true
      }
    })
  }, [users, filterValue, filterColumn])

  const handlePageChange = (newPage: number) => {
    setPage(newPage)
  }

  const handlePageSizeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSize = Number(e.target.value)
    setPageSize(newSize)
    setPage(1)
  }

  const handleExportToExcel = () => {
    // Préparer les données pour l'export
    const dataToExport = filteredUsers.map((user) => ({
      Nom: user.name || "",
      "Nom d'utilisateur": user.username || "",
      Email: user.email || "",
      Role: user.role || "",
      "Email vérifié": user.emailVerified ? "Oui" : "Non",
    }))

    // Créer un workbook et ajouter les données
    const worksheet = XLSXUtils.json_to_sheet(dataToExport)
    const workbook = XLSXUtils.book_new()
    XLSXUtils.book_append_sheet(workbook, worksheet, "Utilisateurs")

    // Générer le fichier et le télécharger
    XLSXWriteFile(workbook, "Cohead_utilisateurs.xlsx")
  }

  // const handleSelectUser = (userId: string, isSelected: boolean) => {
  //   const newSelectedUsers = new Set(selectedUsers)
  //   if (isSelected) {
  //     newSelectedUsers.add(userId)
  //   } else {
  //     newSelectedUsers.delete(userId)
  //   }
  //   setSelectedUsers(newSelectedUsers)
  // }

  const handleSelectAll = (isSelected: boolean) => {
    if (isSelected) {
      const allUserIds = new Set(filteredUsers.map((user) => user.id))
      setSelectedUsers(allUserIds)
    } else {
      setSelectedUsers(new Set())
    }
  }

  const handleBulkDelete = () => {
    if (selectedUsers.size === 0) {
      toast.error("Aucun utilisateur sélectionné")
      return
    }
    setShowBulkDeleteModal(true)
  }

  const confirmBulkDelete = () => {
    bulkDeleteMutation.mutate({
      userIds: Array.from(selectedUsers),
    })
  }

  // const isAllSelected = filteredUsers.length > 0 && selectedUsers.size === filteredUsers.length
  // const isIndeterminate = selectedUsers.size > 0 && selectedUsers.size < filteredUsers.length

  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex w-full flex-wrap justify-between gap-4">
        {/* Filtres */}
        <div className="mb-4 flex gap-4">
          <Input
            isClearable
            className="w-full sm:min-w-[200px] sm:max-w-[44%]"
            placeholder="Rechercher..."
            startContent={<Search className="text-default-300" size={18} />}
            value={filterValue}
            onClear={() => setFilterValue("")}
            onValueChange={setFilterValue}
          />
          <Select
            className="w-full sm:min-w-[150px] sm:max-w-[200px]"
            selectedKeys={[filterColumn]}
            onChange={(e) => setFilterColumn(e.target.value)}
            aria-label="Filtrer par"
          >
            <SelectItem key="name" value="name">
              Nom
            </SelectItem>
            <SelectItem key="username" value="username">
              Nom d&apos;utilisateur
            </SelectItem>
            <SelectItem key="email" value="email">
              Email
            </SelectItem>
            <SelectItem key="role" value="role">
              Role
            </SelectItem>
          </Select>
        </div>

        <div className="flex gap-2">
          {selectedUsers.size > 0 && (
            <Button
              color="danger"
              variant="flat"
              startContent={<Trash2 size={18} />}
              onPress={handleBulkDelete}
              isLoading={bulkDeleteMutation.isPending}
            >
              Supprimer ({selectedUsers.size})
            </Button>
          )}
          <Button
            color="primary"
            variant="flat"
            startContent={<DownloadIcon size={18} />}
            onPress={handleExportToExcel}
          >
            Exporter
          </Button>
          <CreateUserModal onUserCreated={handleUsersMutated} />
        </div>
      </div>

      {/* Table */}
      <Table
        aria-label="Tableau des utilisateurs"
        selectionMode="multiple"
        selectedKeys={selectedUsers}
        onSelectionChange={(keys) => {
          if (keys === "all") {
            handleSelectAll(true)
          } else {
            setSelectedUsers(new Set(keys as Set<string>))
          }
        }}
        bottomContent={
          <div className="flex w-full items-center justify-between">
            <Select
              className="w-28"
              size="sm"
              label="Lignes"
              value={pageSize.toString()}
              onChange={handlePageSizeChange}
            >
              <SelectItem key="10" value="10">
                10
              </SelectItem>
              <SelectItem key="15" value="15">
                15
              </SelectItem>
              <SelectItem key="25" value="25">
                25
              </SelectItem>
              <SelectItem key="50" value="50">
                50
              </SelectItem>
            </Select>
            <Pagination
              showControls
              showShadow
              color="primary"
              page={page}
              total={totalPages}
              onChange={handlePageChange}
            />
          </div>
        }
      >
        <TableHeader>
          {usersColumns.map((column) => (
            <TableColumn
              key={column.uid}
              hideHeader={column.uid === "actions"}
              align={column.uid === "actions" ? "center" : "start"}
            >
              {column.name}
            </TableColumn>
          ))}
        </TableHeader>
        <TableBody
          items={filteredUsers}
          loadingContent={<Spinner label="Chargement..." />}
          loadingState={isLoading ? "loading" : "idle"}
          emptyContent="Aucun utilisateur trouvé"
        >
          {(user) => (
            <TableRow key={user.id}>
              {(columnKey) => (
                <TableCell>
                  {RenderCell({
                    user: user,
                    columnKey: columnKey.toString(),
                    onMutation: handleUsersMutated,
                  })}
                </TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>

      {/* Bulk Delete Confirmation Modal */}
      <Modal
        isOpen={showBulkDeleteModal}
        onClose={() => setShowBulkDeleteModal(false)}
        size="md"
        placement="center"
        backdrop="blur"
      >
        <ModalContent>
          <ModalHeader>
            <h3 className="text-lg font-semibold">Confirmer la suppression</h3>
          </ModalHeader>
          <ModalBody>
            <p>
              Êtes-vous sûr de vouloir supprimer <strong>{selectedUsers.size}</strong> utilisateur(s) ?
            </p>
            <p className="text-sm text-danger">
              Cette action est irréversible et supprimera définitivement les comptes utilisateurs sélectionnés.
            </p>
          </ModalBody>
          <ModalFooter>
            <Button
              variant="light"
              onPress={() => setShowBulkDeleteModal(false)}
              disabled={bulkDeleteMutation.isPending}
            >
              Annuler
            </Button>
            <Button color="danger" onPress={confirmBulkDelete} isLoading={bulkDeleteMutation.isPending}>
              Supprimer
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  )
}
