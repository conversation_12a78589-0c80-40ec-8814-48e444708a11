"use client"

import React, { useEffect, useState } from "react"
import { Archive, BarChart3, Edit, Eye, Pause, Play, Plus, <PERSON>otateCcw, Trash2 } from "lucide-react"
import { toast } from "react-toastify"

import { useConfirmationDialog } from "@/components/ui/confirmation-dialog"
import { trpc } from "@/lib/trpc/client"
import type { RouterOutputs } from "@/lib/trpc/utils"
import { Button } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@nextui-org/table"
import { Tooltip } from "@nextui-org/tooltip"
import { FormStatus } from "@prisma/client"

import { FormPreviewModal } from "./form-preview-modal"

// Define proper types for form data
type FormData = RouterOutputs["form"]["getAll"]["data"][0]

interface FormsTableProps {
  initialPagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
  reload: boolean
  setReload: React.Dispatch<React.SetStateAction<boolean>>
  onArchive: () => void
}

export const FormsTable: React.FC<FormsTableProps> = ({ initialPagination, reload, setReload, onArchive }) => {
  const [page] = useState(1)
  const [pageSize] = useState(15)
  const [selectedForm, setSelectedForm] = useState<FormData | null>(null)
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)

  const utils = trpc.useUtils()

  const { showConfirmation, ConfirmationDialog } = useConfirmationDialog()

  // Use tRPC query instead of local state for forms data
  const { data: formsData, refetch } = trpc.form.getAll.useQuery({ page, pageSize })

  const forms = formsData?.data || []
  const pagination = formsData?.pagination || initialPagination

  // Handle reload trigger
  useEffect(() => {
    if (reload) {
      refetch()
      setReload(false)
    }
  }, [reload, setReload, refetch])

  const deleteForm = trpc.form.delete.useMutation({
    onSuccess: () => {
      toast.success("Formulaire supprimé avec succès")
      // Invalidate queries to trigger automatic refetch
      utils.form.getAll.invalidate()
      utils.form.countByStatus.invalidate()
    },
    onError: (error) => {
      toast.error(error.message || "Erreur lors de la suppression")
    },
  })

  const activateForm = trpc.form.activate.useMutation({
    onSuccess: () => {
      toast.success("Formulaire activé avec succès")
      // Invalidate queries to trigger automatic refetch
      utils.form.getAll.invalidate()
    },
    onError: (error) => {
      toast.error(error.message || "Erreur lors de l'activation")
    },
  })

  const deactivateForm = trpc.form.deactivate.useMutation({
    onSuccess: () => {
      toast.success("Formulaire désactivé avec succès")
      // Invalidate queries to trigger automatic refetch
      utils.form.getAll.invalidate()
    },
    onError: (error) => {
      toast.error(error.message || "Erreur lors de la désactivation")
    },
  })

  const archiveForm = trpc.form.archive.useMutation({
    onSuccess: () => {
      toast.success("Formulaire archivé avec succès")
      // Invalidate all form-related queries to trigger automatic refetch
      utils.form.getAll.invalidate()
      utils.form.getArchived.invalidate()
      utils.form.countByStatus.invalidate()
      onArchive()
    },
    onError: (error) => {
      toast.error(error.message || "Erreur lors de l'archivage")
    },
  })

  const unarchiveForm = trpc.form.unarchive.useMutation({
    onSuccess: () => {
      toast.success("Formulaire désarchivé avec succès")
      // Invalidate all form-related queries to trigger automatic refetch
      utils.form.getAll.invalidate()
      utils.form.getArchived.invalidate()
      utils.form.countByStatus.invalidate()
    },
    onError: (error) => {
      toast.error(error.message || "Erreur lors du désarchivage")
    },
  })

  const getStatusColor = (status: FormStatus) => {
    switch (status) {
      case FormStatus.ACTIVE:
        return "success"
      case FormStatus.INACTIVE:
        return "warning"
      case FormStatus.DRAFT:
        return "default"
      case FormStatus.ARCHIVED:
        return "danger"
      default:
        return "default"
    }
  }

  const getStatusLabel = (status: FormStatus) => {
    switch (status) {
      case FormStatus.ACTIVE:
        return "Actif"
      case FormStatus.INACTIVE:
        return "Inactif"
      case FormStatus.DRAFT:
        return "Brouillon"
      case FormStatus.ARCHIVED:
        return "Archivé"
      default:
        return status
    }
  }

  const handlePreview = (form: FormData) => {
    setSelectedForm(form)
    setIsPreviewOpen(true)
  }

  const handleDelete = (form: FormData) => {
    const hasSubmissions = form._count.submissions > 0

    if (hasSubmissions) {
      showConfirmation({
        title: "Supprimer le formulaire avec réponses",
        message: `Ce formulaire contient ${form._count.submissions} réponse(s). Supprimer le formulaire supprimera également toutes les réponses associées. Cette action est irréversible.`,
        confirmText: "Supprimer tout",
        cancelText: "Annuler",
        variant: "danger",
        onConfirm: () => deleteForm.mutate({ id: form.id, force: true }),
      })
    } else {
      showConfirmation({
        title: "Supprimer le formulaire",
        message: "Êtes-vous sûr de vouloir supprimer ce formulaire ? Cette action est irréversible.",
        confirmText: "Supprimer",
        cancelText: "Annuler",
        variant: "danger",
        onConfirm: () => deleteForm.mutate({ id: form.id, force: false }),
      })
    }
  }

  const handleToggleActive = (form: FormData) => {
    if (form.isActive) {
      deactivateForm.mutate(form.id)
    } else {
      activateForm.mutate(form.id)
    }
  }

  const handleArchive = (formId: string) => {
    showConfirmation({
      title: "Archiver le formulaire",
      message: "Êtes-vous sûr de vouloir archiver ce formulaire ? Il ne sera plus visible dans la liste principale.",
      confirmText: "Archiver",
      cancelText: "Annuler",
      variant: "warning",
      onConfirm: () => archiveForm.mutate(formId),
    })
  }

  const handleUnarchive = (formId: string) => {
    showConfirmation({
      title: "Désarchiver le formulaire",
      message: "Êtes-vous sûr de vouloir désarchiver ce formulaire ?",
      confirmText: "Désarchiver",
      cancelText: "Annuler",
      variant: "info",
      onConfirm: () => unarchiveForm.mutate(formId),
    })
  }

  return (
    <>
      <Card>
        <CardHeader className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">Formulaires</h3>
            <p className="text-sm text-default-500">
              {pagination.total} formulaire{pagination.total > 1 ? "s" : ""} au total
            </p>
          </div>
          <Button color="primary" startContent={<Plus className="size-4" />} href="/admin/forms/create" as="a">
            Nouveau formulaire
          </Button>
        </CardHeader>
        <CardBody>
          <Table aria-label="Formulaires">
            <TableHeader>
              <TableColumn>TITRE</TableColumn>
              <TableColumn>STATUT</TableColumn>
              <TableColumn>QUESTIONS</TableColumn>
              <TableColumn>SOUMISSIONS</TableColumn>
              <TableColumn>CRÉÉ PAR</TableColumn>
              <TableColumn>ACTIONS</TableColumn>
            </TableHeader>
            <TableBody>
              {forms.map((form) => (
                <TableRow key={form.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium">{form.title}</p>
                      {form.description && (
                        <p className="max-w-xs truncate text-sm text-default-500">{form.description}</p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Chip size="sm" color={getStatusColor(form.status)} variant="flat">
                        {getStatusLabel(form.status)}
                      </Chip>
                      {form.isActive && (
                        <Chip size="sm" color="success" variant="dot">
                          Actif
                        </Chip>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{form.questions.length}</TableCell>
                  <TableCell>{form._count.submissions}</TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <p>{form.creator.name || "Utilisateur"}</p>
                      <p className="text-default-500">{form.creator.email}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Tooltip content="Aperçu">
                        <Button isIconOnly size="sm" variant="light" onPress={() => handlePreview(form)}>
                          <Eye className="size-4" />
                        </Button>
                      </Tooltip>

                      <Tooltip content="Modifier">
                        <Button isIconOnly size="sm" variant="light" href={`/admin/forms/${form.id}/edit`} as="a">
                          <Edit className="size-4" />
                        </Button>
                      </Tooltip>

                      <Tooltip content={form.isActive ? "Désactiver" : "Activer"}>
                        <Button
                          isIconOnly
                          size="sm"
                          variant="light"
                          color={form.isActive ? "warning" : "success"}
                          onPress={() => handleToggleActive(form)}
                          isLoading={activateForm.isPending || deactivateForm.isPending}
                        >
                          {form.isActive ? <Pause className="size-4" /> : <Play className="size-4" />}
                        </Button>
                      </Tooltip>

                      <Tooltip content="Statistiques">
                        <Button isIconOnly size="sm" variant="light" href={`/admin/forms/${form.id}/analytics`} as="a">
                          <BarChart3 className="size-4" />
                        </Button>
                      </Tooltip>

                      {form.status === FormStatus.ARCHIVED ? (
                        <Tooltip content="Désarchiver">
                          <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                            color="primary"
                            onPress={() => handleUnarchive(form.id)}
                            isLoading={unarchiveForm.isPending}
                          >
                            <RotateCcw className="size-4" />
                          </Button>
                        </Tooltip>
                      ) : (
                        <Tooltip content="Archiver">
                          <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                            color="warning"
                            onPress={() => handleArchive(form.id)}
                            isLoading={archiveForm.isPending}
                          >
                            <Archive className="size-4" />
                          </Button>
                        </Tooltip>
                      )}

                      <Tooltip content="Supprimer">
                        <Button
                          isIconOnly
                          size="sm"
                          variant="light"
                          color="danger"
                          onPress={() => handleDelete(form)}
                          isLoading={deleteForm.isPending}
                        >
                          <Trash2 className="size-4" />
                        </Button>
                      </Tooltip>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardBody>
      </Card>

      {/* Form Preview Modal*/}
      <FormPreviewModal isOpen={isPreviewOpen} onClose={() => setIsPreviewOpen(false)} form={selectedForm} />

      {/* Confirmation Dialog */}
      <ConfirmationDialog />
    </>
  )
}
