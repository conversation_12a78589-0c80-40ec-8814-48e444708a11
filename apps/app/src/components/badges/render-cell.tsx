"use client"

import React, { useState } from "react"
import { <PERSON><PERSON><PERSON>, Trash } from "lucide-react"
import { HexColorPicker } from "react-colorful"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { generateBadgePalette, loadBadgeColorsFromDB } from "@/lib/utils/color-utils"
import { logger } from "@coheadcoaching/lib"
import { Button } from "@nextui-org/button"
import { Input } from "@nextui-org/input"
import { Modal, ModalBody, ModalContent, ModalFooter, ModalHeader } from "@nextui-org/modal"
import { Tooltip } from "@nextui-org/tooltip"
import { Prisma } from "@prisma/client"

const isRenderable = (child: unknown) => {
  return React.isValidElement(child) || typeof child === "string" || typeof child === "number"
}

type BadgeType = Prisma.BadgeGetPayload<{
  include: {
    _count: {
      select: {
        agents: true
      }
    }
  }
}>

interface Props {
  badge: BadgeType
  columnKey: string | React.Key
  onMutation?: () => void
}

export const RenderCell = ({ badge, columnKey, onMutation }: Props) => {
  //@ts-expect-error Any type detected
  const cellValue = badge[columnKey]
  switch (columnKey) {
    case "agentsCount":
      return badge._count.agents
    case "actions":
      return (
        <div className="flex items-center gap-4">
          <EditBadgeModal badge={badge} onBadgeUpdated={onMutation} />
          <DeleteBadgeModal badge={badge} onBadgeDeleted={onMutation} />
        </div>
      )
    default:
      return isRenderable(cellValue) ? cellValue : "<___>"
  }
}

/****
 ** Modals down here
 */

interface EditBadgeModalProps {
  badge: BadgeType
  onBadgeUpdated?: () => void
}

const EditBadgeModal = ({ badge, onBadgeUpdated }: EditBadgeModalProps) => {
  const utils = trpc.useUtils()
  const [showModal, setShowModal] = useState(false)
  const [formData, setFormData] = useState({
    id: badge.id,
    title: badge.title,
    primaryColor: badge.primaryColor || "#e4d4f4",
  })
  const [color, setColor] = useState(badge.primaryColor || "#e4d4f4")
  const [colorInput, setColorInput] = useState(badge.primaryColor || "#e4d4f4")
  const [hslColorVariants, setHslColorVariants] = useState(
    badge.primaryColor
      ? loadBadgeColorsFromDB({
        primaryColor: badge.primaryColor,
        backgroundLight: badge.backgroundLight,
        textLight: badge.textLight,
        backgroundDark: badge.backgroundDark,
        textDark: badge.textDark,
      })
      : generateBadgePalette("#e4d4f4")
  )
  const [errors, setErrors] = useState<Record<string, string>>({})

  const badgeUpdate = trpc.badge.update.useMutation({
    onSuccess: () => {
      setShowModal(false)
      toast.success("Badge mis à jour avec succès!")
      utils.user.getAll.invalidate()
      if (onBadgeUpdated) {
        onBadgeUpdated()
      }
    },
    onError: (error) => {
      logger.log(error)
      toast.error("Erreur lors de la mise à jour du badge")
    },
  })

  const handleOpenModal = () => {
    setShowModal(true)
  }

  const handleCloseModal = () => {
    setShowModal(false)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleColorChange = (newColor: string) => {
    setColor(newColor)
    setColorInput(newColor)
    setHslColorVariants(generateBadgePalette(newColor))
    setFormData((prev) => ({ ...prev, primaryColor: newColor }))
    // Clear color input error if it exists
    if (errors.colorInput) {
      setErrors((prev) => ({ ...prev, colorInput: "" }))
    }
  }

  const handleColorInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setColorInput(value)

    // Validate hex color format
    const hexColorRegex = /^#[0-9A-Fa-f]{6}$/
    if (hexColorRegex.test(value)) {
      setColor(value)
      setHslColorVariants(generateBadgePalette(value))
      setFormData((prev) => ({ ...prev, primaryColor: value }))
      // Clear error if valid
      if (errors.colorInput) {
        setErrors((prev) => ({ ...prev, colorInput: "" }))
      }
    } else if (value.length === 7) {
      // Only show error if the input is complete but invalid
      setErrors((prev) => ({ ...prev, colorInput: "Format invalide. Utilisez le format #RRGGBB (ex: #FF5733)" }))
    }
  }

  const handleSubmit = () => {
    badgeUpdate.mutate({
      ...formData,
      primaryColor: color,
    })
  }

  return (
    <>
      <Tooltip content="Modifier" color="secondary">
        <Button
          isIconOnly
          variant="light"
          size="sm"
          radius="full"
          onPress={handleOpenModal}
          className="text-default-500 transition-colors hover:text-secondary"
        >
          <Pencil size={18} />
        </Button>
      </Tooltip>

      <Modal
        isOpen={showModal}
        onClose={handleCloseModal}
        scrollBehavior="inside"
        size="md"
        placement="center"
        backdrop="blur"
        classNames={{
          body: "py-6",
          closeButton: "hover:bg-default-100",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-xl font-bold">Modifier badge</h2>
            <p className="text-sm text-default-500">Mise à jour des informations du badge</p>
          </ModalHeader>
          <ModalBody>
            <form className="flex flex-col gap-6">
              <Input
                label="Titre"
                name="title"
                value={formData.title || ""}
                onChange={handleInputChange}
                placeholder="Saisissez le titre"
                size="lg"
                variant="bordered"
                labelPlacement="outside"
                classNames={{
                  label: "text-sm font-medium text-default-700 dark:text-default-500",
                }}
              />

              <div className="flex flex-col gap-4">
                <div className="text-sm font-medium">Couleur du badge</div>
                <div className="flex flex-col gap-4">
                  <HexColorPicker
                    color={color}
                    onChange={handleColorChange}
                  />
                  <Input
                    label="Code couleur (optionnel)"
                    name="colorInput"
                    value={colorInput}
                    onChange={handleColorInputChange}
                    placeholder="#FF5733"
                    size="sm"
                    variant="bordered"
                    labelPlacement="outside"
                    isInvalid={!!errors.colorInput}
                    errorMessage={errors.colorInput}
                    description="Entrez un code couleur hexadécimal"
                    classNames={{
                      label: "text-xs font-medium",
                      description: "text-xs text-default-400",
                    }}
                  />
                </div>
                <div className="text-xs text-default-500">
                  Couleur sélectionnée: {color}
                </div>
              </div>

              <div className="flex flex-col gap-4">
                <div className="text-sm font-medium">Aperçu des badges</div>
                <div className="flex flex-col gap-4 rounded-lg border border-default-200 p-4">
                  <div className="flex flex-col gap-2">
                    <div className="text-xs text-default-500 font-medium">Thème clair</div>
                    <div className="flex items-center justify-center rounded-md bg-background p-4 border border-default-100 light">
                      <span
                        className="inline-block w-fit rounded-full px-3 py-1 text-xs font-semibold transition-all duration-200 max-w-full break-words"
                        style={{
                          backgroundColor: `hsl(${hslColorVariants["100-light"]})`,
                          color: `hsl(${hslColorVariants["light"]})`
                        }}
                      >
                        {formData.title || "Nom du badge"}
                      </span>
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <div className="text-xs text-default-500 font-medium">Thème sombre</div>
                    <div className="flex items-center justify-center rounded-md bg-background p-4 border border-default-700 dark">
                      <span
                        className="inline-block w-fit rounded-full px-3 py-1 text-xs font-semibold transition-all duration-200 max-w-full break-words"
                        style={{
                          backgroundColor: `hsl(${hslColorVariants["100-dark"]})`,
                          color: `hsl(${hslColorVariants["dark"]})`
                        }}
                      >
                        {formData.title || "Nom du badge"}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </ModalBody>
          <ModalFooter>
            <Button onPress={handleCloseModal} variant="light" color="danger">
              Annuler
            </Button>
            <Button isLoading={badgeUpdate.isPending} onPress={handleSubmit} color="secondary" variant="flat">
              Enregistrer les modifications
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}

interface DeleteBadgeModalProps {
  badge: BadgeType
  onBadgeDeleted?: () => void
}

const DeleteBadgeModal = ({ badge, onBadgeDeleted }: DeleteBadgeModalProps) => {
  const utils = trpc.useUtils()
  const [showModal, setShowModal] = useState(false)

  const badgeDelete = trpc.badge.delete.useMutation({
    onSuccess: () => {
      setShowModal(false)
      toast.success("Badge supprimé avec succès!")
      utils.badge.getAll.invalidate()
      if (onBadgeDeleted) {
        onBadgeDeleted()
      }
    },
    onError: (error) => {
      logger.log(error)
      toast.error("Erreur lors de la suppression du badge")
    },
  })

  const handleOpenModal = () => {
    if (badge._count.agents > 0) {
      toast.error("Vous ne pouvez pas supprimer ce badge quand des agents sont associés!")
      return
    }
    setShowModal(true)
  }

  const handleCloseModal = () => {
    setShowModal(false)
  }

  const handleBadgeDelete = () => {
    badgeDelete.mutate(badge.id)
  }

  return (
    <>
      <Tooltip content="Supprimer" color="danger">
        <Button
          isIconOnly
          variant="light"
          size="sm"
          radius="full"
          onPress={handleOpenModal}
          className="text-default-500 transition-colors hover:text-danger"
        >
          <Trash size={18} />
        </Button>
      </Tooltip>

      <Modal
        isOpen={showModal}
        onClose={handleCloseModal}
        size="md"
        placement="center"
        backdrop="blur"
        classNames={{
          body: "py-6",
          closeButton: "hover:bg-default-100",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-xl font-bold text-danger">Confirmation de suppression</h2>
            <p className="text-sm text-default-500">Cette action ne peut pas être annulée</p>
          </ModalHeader>
          <ModalBody>
            <div className="rounded-lg bg-danger-50 p-4">
              <p className="text-sm text-danger">
                Vous êtes sur le point de supprimer définitivement ce badge. Cette action supprimera toutes les données
                associées à ce badge et ne peut pas être annulée.
              </p>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button onPress={handleCloseModal} variant="light">
              Annuler
            </Button>
            <Button isLoading={badgeDelete.isPending} onPress={handleBadgeDelete} color="danger" variant="flat">
              Supprimer définitivement
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}
