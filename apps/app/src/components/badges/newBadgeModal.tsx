"use client"

import React, { ChangeEvent, useState } from "react"
import { UserPlus2 } from "lucide-react"
import { HexColorPicker } from "react-colorful"
import { toast } from "react-toastify"
import { z } from "zod"

import { trpc } from "@/lib/trpc/client"
import { generateBadgePalette } from "@/lib/utils/color-utils"
import { logger } from "@coheadcoaching/lib"
import { Button } from "@nextui-org/button"
import { Input } from "@nextui-org/input"
import { <PERSON>dal, ModalBody, ModalContent, ModalFooter, ModalHeader } from "@nextui-org/modal"

// Schéma de validation Zod pour les données du formulaire
const badgeCreateSchema = z.object({
  title: z.string().min(1, "Le titre est requis"),
  primaryColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Invalid hex color format").optional(),
})

// Type pour les données du formulaire
type FormData = z.infer<typeof badgeCreateSchema>

// Props du composant
interface CreateBadgeModalProps {
  onBadgeCreated?: () => void
}

const CreateBadgeModal: React.FC<CreateBadgeModalProps> = ({ onBadgeCreated }) => {
  const utils = trpc.useUtils()
  const [showModal, setShowModal] = useState<boolean>(false)
  const [formData, setFormData] = useState<FormData>({
    title: "",
    primaryColor: "#e4d4f4",
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  const [color, setColor] = useState("#e4d4f4")
  const [colorInput, setColorInput] = useState("#e4d4f4")

  const [hslColorVariants, setHslColorVariants] = useState(generateBadgePalette(color))

  const badgeCreate = trpc.badge.create.useMutation({
    onSuccess: () => {
      setShowModal(false)
      toast.success("Badge créé avec succès!")
      utils.user.getAll.invalidate()
      // Réinitialisation du formulaire
      setFormData({
        title: "",
        primaryColor: "#e4d4f4",
      })
      setColor("#e4d4f4")
      setColorInput("#e4d4f4")
      setHslColorVariants(generateBadgePalette("#e4d4f4"))
      setErrors({})
      if (onBadgeCreated) {
        onBadgeCreated()
      }
    },
    onError: (error) => {
      logger.log(error)
      // Display specific error message from server
      if (error.message) {
        toast.error(error.message)
      } else {
        toast.error("Erreur lors de la création du badge")
      }
    },
  })

  const handleOpenModal = () => {
    setShowModal(true)
  }

  const handleCloseModal = () => {
    setShowModal(false)
    // Réinitialisation du formulaire
    setFormData({
      title: "",
      primaryColor: "#e4d4f4",
    })
    setColor("#e4d4f4")
    setColorInput("#e4d4f4")
    setHslColorVariants(generateBadgePalette("#e4d4f4"))
    setErrors({})
  }

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
    // Effacer l'erreur du champ modifié
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }))
    }
  }

  const handleColorChange = (newColor: string) => {
    setColor(newColor)
    setColorInput(newColor)
    setHslColorVariants(generateBadgePalette(newColor))
    setFormData((prev) => ({ ...prev, primaryColor: newColor }))
    // Clear color input error if it exists
    if (errors.colorInput) {
      setErrors((prev) => ({ ...prev, colorInput: "" }))
    }
  }

  const handleColorInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setColorInput(value)

    // Validate hex color format
    const hexColorRegex = /^#[0-9A-Fa-f]{6}$/
    if (hexColorRegex.test(value)) {
      setColor(value)
      setHslColorVariants(generateBadgePalette(value))
      setFormData((prev) => ({ ...prev, primaryColor: value }))
      // Clear error if valid
      if (errors.colorInput) {
        setErrors((prev) => ({ ...prev, colorInput: "" }))
      }
    } else if (value.length === 7) {
      // Only show error if the input is complete but invalid
      setErrors((prev) => ({ ...prev, colorInput: "Format invalide. Utilisez le format #RRGGBB (ex: #FF5733)" }))
    }
  }

  const handleSubmit = () => {
    try {
      const validatedData = badgeCreateSchema.parse({
        ...formData,
        primaryColor: color,
      })
      badgeCreate.mutate(validatedData)
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldErrors = error.flatten().fieldErrors
        const errorMessages = Object.keys(fieldErrors).reduce(
          (acc, key) => {
            acc[key] = fieldErrors[key]?.join(", ") || ""
            return acc
          },
          {} as Record<string, string>
        )
        setErrors(errorMessages)
        toast.error("Veuillez corriger les erreurs dans le formulaire")
      }
    }
  }

  return (
    <>
      <Button
        color="primary"
        variant="flat"
        startContent={<UserPlus2 size={18} />}
        onPress={handleOpenModal}
        className="mb-4"
      >
        Créer un Badge
      </Button>

      <Modal
        isOpen={showModal}
        onClose={handleCloseModal}
        scrollBehavior="inside"
        size="md"
        placement="center"
        backdrop="blur"
        classNames={{
          body: "py-6",
          closeButton: "hover:bg-default-100",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-xl font-bold">Nouveau badge</h2>
            <p className="text-sm text-default-500">Ajoutez un nouveau badge pour vos agents</p>
          </ModalHeader>
          <ModalBody>
            <form onSubmit={handleSubmit} className="flex flex-col gap-6">
              <Input
                label="Titre du badge"
                name="title"
                value={formData.title || ""}
                onChange={handleInputChange}
                placeholder="Saisissez le titre complet"
                size="lg"
                variant="bordered"
                isRequired
                isDisabled={badgeCreate.isPending}
                labelPlacement="outside"
                isInvalid={!!errors.title}
                errorMessage={errors.title}
                classNames={{
                  label: "text-sm font-medium",
                }}
              />
              <div className="flex flex-col gap-4">
                <div className="text-sm font-medium">Couleur du badge</div>
                <div className="flex flex-col gap-4">
                  <HexColorPicker
                    color={color}
                    onChange={handleColorChange}
                  />
                  <Input
                    label="Code couleur (optionnel)"
                    name="colorInput"
                    value={colorInput}
                    onChange={handleColorInputChange}
                    placeholder="#FF5733"
                    size="sm"
                    variant="bordered"
                    labelPlacement="outside"
                    isInvalid={!!errors.colorInput}
                    errorMessage={errors.colorInput}
                    description="Entrez un code couleur hexadécimal"
                    classNames={{
                      label: "text-xs font-medium",
                      description: "text-xs text-default-400",
                    }}
                  />
                </div>
                <div className="text-xs text-default-500">
                  Couleur sélectionnée: {color}
                </div>
              </div>
              <div className="flex flex-col gap-4">
                <div className="text-sm font-medium">Aperçu des badges</div>
                <div className="flex flex-col gap-4 rounded-lg border border-default-200 p-4">
                  <div className="flex flex-col gap-2">
                    <div className="text-xs text-default-500 font-medium">Thème clair</div>
                    <div className="flex items-center justify-center rounded-md bg-background p-4 border border-default-100 light">
                      <span
                        className="inline-block w-fit rounded-full px-3 py-1 text-xs font-semibold transition-all duration-200 max-w-full break-words"
                        style={{
                          backgroundColor: `hsl(${hslColorVariants["100-light"]})`,
                          color: `hsl(${hslColorVariants["light"]})`
                        }}
                      >
                        {formData.title || "Nom du badge"}
                      </span>
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <div className="text-xs text-default-500 font-medium">Thème sombre</div>
                    <div className="flex items-center justify-center rounded-md bg-background p-4 border border-default-700 dark">
                      <span
                        className="inline-block w-fit rounded-full px-3 py-1 text-xs font-semibold transition-all duration-200 max-w-full break-words"
                        style={{
                          backgroundColor: `hsl(${hslColorVariants["100-dark"]})`,
                          color: `hsl(${hslColorVariants["dark"]})`
                        }}
                      >
                        {formData.title || "Nom du badge"}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </ModalBody>
          <ModalFooter>
            <Button onPress={handleCloseModal} variant="light" color="danger">
              Annuler
            </Button>
            <Button isLoading={badgeCreate.isPending} onPress={handleSubmit} color="primary" variant="flat">
              Créer le badge
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}

export default CreateBadgeModal
