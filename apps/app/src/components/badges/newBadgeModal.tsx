"use client"

import React, { ChangeEvent, useState } from "react"
import { UserPlus2 } from "lucide-react"
import { HexColorPicker } from "react-colorful"
import { toast } from "react-toastify"
import { z } from "zod"

import { trpc } from "@/lib/trpc/client"
import { logger } from "@coheadcoaching/lib"
import { Button } from "@nextui-org/button"
import { Input } from "@nextui-org/input"
import { Modal, ModalBody, ModalContent, ModalFooter, ModalHeader } from "@nextui-org/modal"
import { generateBadgePalette } from "@/lib/utils/color-utils"

// Schéma de validation Zod pour les données du formulaire
const badgeCreateSchema = z.object({
  title: z.string().min(1, "Le titre est requis"),
  agentId: z.number().optional(),
})

// Type pour les données du formulaire
type FormData = z.infer<typeof badgeCreateSchema>

// Props du composant
interface CreateBadgeModalProps {
  onBadgeCreated?: () => void
}

const CreateBadgeModal: React.FC<CreateBadgeModalProps> = ({ onBadgeCreated }) => {
  const utils = trpc.useUtils()
  const [showModal, setShowModal] = useState<boolean>(false)
  const [formData, setFormData] = useState<FormData>({
    title: "",
    agentId: undefined,
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  const [color, setColor] = useState("#6633ff");

  const [hslColorVariants, setHslColorVariants] = useState(generateBadgePalette(color))

  const badgeCreate = trpc.badge.create.useMutation({
    onSuccess: () => {
      setShowModal(false)
      toast.success("Badge créé avec succès!")
      utils.user.getAll.invalidate()
      // Réinitialisation du formulaire
      setFormData({
        title: "",
      })
      setErrors({})
      if (onBadgeCreated) {
        onBadgeCreated()
      }
    },
    onError: (error) => {
      logger.log(error)
    },
  })

  const handleOpenModal = () => {
    setShowModal(true)
  }

  const handleCloseModal = () => {
    setShowModal(false)
    // Réinitialisation du formulaire
    setFormData({
      title: "",
      agentId: undefined,
    })

    setErrors({})
  }

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
    // Effacer l'erreur du champ modifié
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }))
    }
  }

  const handleSubmit = () => {
    try {
      const validatedData = badgeCreateSchema.parse({
        ...formData,
      })
      badgeCreate.mutate(validatedData)
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldErrors = error.flatten().fieldErrors
        const errorMessages = Object.keys(fieldErrors).reduce(
          (acc, key) => {
            acc[key] = fieldErrors[key]?.join(", ") || ""
            return acc
          },
          {} as Record<string, string>
        )
        setErrors(errorMessages)
        toast.error("Veuillez corriger les erreurs dans le formulaire")
      }
    }
  }

  return (
    <>
      <Button
        color="primary"
        variant="flat"
        startContent={<UserPlus2 size={18} />}
        onPress={handleOpenModal}
        className="mb-4"
      >
        Créer un Badge
      </Button>

      <Modal
        isOpen={showModal}
        onClose={handleCloseModal}
        scrollBehavior="inside"
        size="md"
        placement="center"
        backdrop="blur"
        classNames={{
          body: "py-6",
          closeButton: "hover:bg-default-100",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-xl font-bold">Nouveau badge</h2>
            <p className="text-sm text-default-500">Ajoutez un nouveau badge pour vos agents</p>
          </ModalHeader>
          <ModalBody>
            <form onSubmit={handleSubmit} className="flex flex-col gap-6">
              <Input
                label="Titre du badge"
                name="title"
                value={formData.title || ""}
                onChange={handleInputChange}
                placeholder="Saisissez le titre complet"
                size="lg"
                variant="bordered"
                isRequired
                isDisabled={badgeCreate.isPending}
                labelPlacement="outside"
                isInvalid={!!errors.title}
                errorMessage={errors.title}
                classNames={{
                  label: "text-sm font-medium",
                }}
              />
              <HexColorPicker
                color={color}
                onChange={(color) => { setColor(color); setHslColorVariants(generateBadgePalette(color)) }}
              />
              <div className="w-full text-center">
                Aperçu des badges
              </div>
              <div className="flex flex-wrap gap-2 justify-center">
                <div className="bg-background light">
                  <span className={`inline-block w-fit rounded-full  bg-[hsl(${hslColorVariants["100-light"]})] px-3 py-1 text-xs font-semibold text-[hsl(${hslColorVariants["light"]})]`}>
                    {formData.title || "Nom du badge"}
                  </span>
                </div>
                <div className="bg-background dark">
                  <span className={`inline-block w-fit rounded-full bg-[hsl(${hslColorVariants["100-dark"]})] px-3 py-1 text-xs font-semibold text-[hsl(${hslColorVariants["dark"]})]`}>
                    {formData.title || "Nom du badge"}
                  </span>
                </div>
              </div>
            </form>
          </ModalBody>
          <ModalFooter>
            <Button onPress={handleCloseModal} variant="light" color="danger">
              Annuler
            </Button>
            <Button isLoading={badgeCreate.isPending} onPress={handleSubmit} color="primary" variant="flat">
              Créer le badge
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}

export default CreateBadgeModal
