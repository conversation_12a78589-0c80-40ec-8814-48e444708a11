"use client"

import React, { useEffect, useState } from "react"
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, User } from "lucide-react"
import CreatableSelect from "react-select/creatable"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Divider } from "@nextui-org/divider"
import { Input, Textarea } from "@nextui-org/input"
import { Select, SelectItem } from "@nextui-org/select"
import { Slider } from "@nextui-org/slider"
import { Tooltip } from "@nextui-org/tooltip"
import { Prisma } from "@prisma/client"

import OptimizePromptButton from "./OptimizePromptButton"
import SkillChip from "./skill-chip"

// Type for agent with skills and prompts
type AgentWithSkillsAndPrompts = Prisma.AgentGetPayload<{
  include: { skills: true; prompts: true }
}>

// Personality options for dropdown
const PERSONALITY_OPTIONS = [
  { value: "friendly", label: "Amical" },
  { value: "professional", label: "Professionnel" },
  { value: "helpful", label: "Serviable" },
  { value: "concise", label: "Concise" },
  { value: "enthusiastic", label: "Enthousiaste" },
  { value: "analytical", label: "Analytique" },
  { value: "creative", label: "Créatif" },
]

// Define the type for our skill option
interface SkillOption {
  value: string | number
  label: string
}

const AgentConfigAdmin = ({ agent }: { agent: AgentWithSkillsAndPrompts }) => {
  // State management for existing fields
  const [model, setModel] = useState(agent.model || "gpt-4")
  const [temperature, setTemperature] = useState(agent.temperature || 1)
  const [personality, setPersonality] = useState(agent.personality || PERSONALITY_OPTIONS[1].value)
  const [selectedPrompt, setSelectedPrompt] = useState<string>(agent.prompts.length > 0 ? agent.prompts[0].body : "")
  const [selectedBadgeId, setSelectedBadgeId] = useState<number>(agent.badgeId || 1)

  // State management for missing fields
  const [title, setTitle] = useState(agent.title || "")
  const [description, setDescription] = useState(agent.description || "")
  const [icon, setIcon] = useState(agent.icon || "")

  // For react-select (skills)
  const [selectedSkills, setSelectedSkills] = useState<SkillOption[]>([])

  // Query hooks
  const { data: allSkills, isLoading: isLoadingSkills } = trpc.skill.getAll.useQuery()
  const { data: allBadges, isLoading: isLoadingBadges } = trpc.badge.getAll.useQuery()

  // Mutations
  const updateAgent = trpc.agent.update.useMutation({
    onSuccess: () => {
      toast.success("Agent mis à jour!")
    },
  })
  const createSkill = trpc.skill.create.useMutation()
  const createPrompt = trpc.prompt.create.useMutation()
  const updatePrompt = trpc.prompt.update.useMutation()

  // Initialize selected skills when data is loaded
  useEffect(() => {
    if (agent.skills) {
      setSelectedSkills(
        agent.skills.map((skill) => ({
          value: String(skill.id),
          label: skill.name,
        }))
      )
    }
  }, [agent.skills])

  // Format skill options for react-select
  const skillOptions: SkillOption[] = allSkills
    ? allSkills.map((skill) => ({ value: skill.id, label: skill.name }))
    : []

  // Handle creating new skill
  const handleCreateSkill = async (inputValue: string) => {
    try {
      // Optimistic update with temporary ID
      const tempId = `temp-${Date.now()}`
      const tempSkill: SkillOption = { value: tempId, label: inputValue }
      setSelectedSkills((prev) => [...prev, tempSkill])

      // Create skill in database
      const newSkill = await createSkill.mutateAsync({
        name: inputValue,
      })

      // Replace temporary skill with real one
      setSelectedSkills((prev) =>
        prev.map((skill) => (skill.value === tempId ? { value: String(newSkill.id), label: newSkill.name } : skill))
      )
    } catch (error: any) {
      console.error("Failed to create skill:", error)

      // Display specific error message from server
      if (error.message) {
        toast.error(error.message)
      } else {
        toast.error("Erreur lors de la création de la compétence.")
      }

      // Remove failed optimistic entry
      setSelectedSkills((prev) =>
        prev.filter((skill) => typeof skill.value === "number" || !String(skill.value).startsWith("temp-"))
      )
    }
  }

  // Handle removing a skill
  const handleRemoveSkill = (skillValue: string) => {
    setSelectedSkills((prev) => prev.filter((skill) => String(skill.value) !== skillValue))
  }

  // Save changes
  const handleSaveChanges = async () => {
    try {
      // Convert skills from react-select format to array of IDs
      const skillIds = selectedSkills
        .filter((skill) => typeof skill.value === "number" || !String(skill.value).startsWith("temp-"))
        .map((skill) => Number(skill.value))

      let promptId

      // Mettre à jour ou créer le prompt
      if (agent.prompts.length > 0) {
        // Mettre à jour le prompt existant
        const updatedPrompt = await updatePrompt.mutateAsync({
          id: agent.prompts[0].id,
          body: selectedPrompt,
          title: agent.prompts[0].title || "Default Title",
          agentId: agent.id,
        })
        promptId = updatedPrompt.id
      } else {
        // Créer un nouveau prompt si nécessaire
        const newPrompt = await createPrompt.mutateAsync({
          title: "Default Title",
          body: selectedPrompt,
          agentId: agent.id,
        })
        promptId = newPrompt.id
      }

      // Send update request with ALL fields
      await updateAgent.mutateAsync({
        id: agent.id,
        title,
        description,
        icon,
        model,
        temperature,
        personality,
        promptId: promptId,
        skills: skillIds,
        badgeId: selectedBadgeId,
      })
    } catch (error) {
      console.error("Failed to update agent:", error)
      toast.error("Erreur de la mise à jour de l'agent. Veuillez réessayer!")
    }
  }

  return (
    <div className="space-y-6">
      {/* Bouton de sauvegarde flottant pour mobile */}
      <div className="sticky bottom-4 right-4 z-10 flex md:hidden">
        <Button
          onPress={handleSaveChanges}
          className="shadow-xl"
          color="primary"
          size="lg"
          radius="full"
          isLoading={updateAgent.isPending}
          startContent={<Save size={18} />}
        >
          Sauvegarder
        </Button>
      </div>

      {/* Section Informations de base */}
      <Card>
        <CardHeader className="flex gap-3">
          <User size={24} />
          <div className="flex flex-col">
            <p className="text-lg font-semibold">Informations de base</p>
            <p className="text-small text-default-500">Identité et description de l&apos;agent</p>
          </div>
        </CardHeader>
        <Divider />
        <CardBody className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <Input
              label="Titre"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Titre de l'agent"
              variant="bordered"
              labelPlacement="outside"
              startContent={<span className="text-xl">{icon}</span>}
            />
            <Input
              label="Emoji ou Icône"
              value={icon}
              onChange={(e) => setIcon(e.target.value)}
              placeholder="Utilisez un emoji (ex: 🤖) ou une icône"
              variant="bordered"
              labelPlacement="outside"
              description="Utilisez un emoji simple comme identifiant visuel"
            />
          </div>
          <Textarea
            label="Description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Description détaillée de l'agent et son objectif"
            variant="bordered"
            labelPlacement="outside"
            minRows={2}
          />

          <div>
            <label className="mb-2 block text-sm font-medium">Badge</label>
            <Select
              placeholder="Sélectionnez un badge"
              value={String(selectedBadgeId)}
              selectedKeys={selectedBadgeId ? [String(selectedBadgeId)] : []}
              onChange={(e) => setSelectedBadgeId(e.target.value ? Number(e.target.value) : 1)}
              isLoading={isLoadingBadges}
              variant="bordered"
            >
              {allBadges
                ? allBadges.map((badge) => (
                  <SelectItem key={String(badge.id)} value={String(badge.id)}>
                    {badge.title || `Badge sans titre (${badge.id})`}
                  </SelectItem>
                ))
                : []}
            </Select>
          </div>
        </CardBody>
      </Card>

      {/* Section Paramètres IA */}
      <Card>
        <CardHeader className="flex gap-3">
          <Bot size={24} />
          <div className="flex flex-col">
            <p className="text-lg font-semibold">Paramètres IA</p>
            <p className="text-small text-default-500">Configuration du modèle et comportement</p>
          </div>
        </CardHeader>
        <Divider />
        <CardBody className="space-y-6">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <Select
              label="Modèle"
              defaultSelectedKeys={[model]}
              onChange={(e) => setModel(e.target.value)}
              variant="bordered"
              labelPlacement="outside"
            >
              <SelectItem key="gpt-4" value="gpt-4" description="Plus performant, recommandé">
                GPT-4
              </SelectItem>
              <SelectItem key="gpt-3.5-turbo" value="gpt-3.5-turbo" description="Plus rapide, économique">
                GPT-3.5 Turbo
              </SelectItem>
            </Select>

            <Select
              label="Personnalité"
              value={personality}
              onChange={(e) => setPersonality(e.target.value)}
              defaultSelectedKeys={[personality]}
              placeholder="Définir une personnalité"
              variant="bordered"
              labelPlacement="outside"
            >
              {PERSONALITY_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </Select>
          </div>

          <div>
            <div className="mb-2 flex justify-between">
              <label className="block text-sm font-medium">Température: {temperature}</label>
              <div className="flex gap-2 text-xs text-default-500">
                <span>Précis</span>
                <span className="mx-auto">Équilibré</span>
                <span>Créatif</span>
              </div>
            </div>
            <Slider
              value={temperature}
              onChange={(value) => setTemperature(Array.isArray(value) ? value[0] : value)}
              minValue={0}
              maxValue={2}
              step={0.1}
              defaultValue={1}
              showTooltip={true}
              // tooltipContent={temperature.toString()}
              marks={[
                { value: 0, label: "0" },
                { value: 1, label: "1" },
                { value: 2, label: "2" },
              ]}
              classNames={{
                filler: temperature < 0.7 ? "bg-blue-600" : temperature > 1.5 ? "bg-purple-600" : "bg-green-600",
              }}
            />
            <p className="mt-2 text-xs text-default-500">
              {temperature < 0.7
                ? "Réponses plus cohérentes et déterministes"
                : temperature > 1.5
                  ? "Réponses plus créatives et variées"
                  : "Équilibre entre créativité et cohérence"}
            </p>
          </div>

          <div>
            <label className="mb-2 block text-sm font-medium">Compétences</label>
            <CreatableSelect<SkillOption, true>
              isMulti
              isClearable
              isLoading={isLoadingSkills}
              options={skillOptions}
              value={selectedSkills}
              onChange={(newValue) => setSelectedSkills(newValue as SkillOption[])}
              onCreateOption={handleCreateSkill}
              placeholder="Sélectionnez ou créez des compétences..."
              formatCreateLabel={(inputValue) => `Créer la compétence "${inputValue}"`}
              className={`custom-react-select`}
              classNamePrefix="react-select"
              components={{
                // Hide the default multi-value component since we're showing custom chips below
                MultiValueContainer: () => null,
                MultiValueRemove: () => null,
              }}
              menuPortalTarget={typeof document !== "undefined" ? document.body : undefined}
              styles={{
                menuPortal: (base) => ({
                  ...base,
                  zIndex: 9999,
                }),
              }}
            />

            {/* Display selected skills as custom chips */}
            {selectedSkills.length > 0 && (
              <div className="mt-4 flex flex-wrap gap-2">
                {selectedSkills.map((skill) => (
                  <SkillChip
                    key={skill.value.toString()}
                    label={skill.label}
                    onRemove={() => handleRemoveSkill(String(skill.value))}
                    isNew={typeof skill.value === "string" && skill.value.startsWith("temp-")}
                  />
                ))}
              </div>
            )}
          </div>
        </CardBody>
      </Card>

      {/* Section Script et Prompt */}
      <Card>
        <CardHeader className="flex gap-3">
          <Sparkles size={24} />
          <div className="flex flex-col">
            <p className="text-lg font-semibold">Script et Prompt</p>
            <p className="text-small text-default-500">Instructions détaillées pour l&apos;agent</p>
          </div>
        </CardHeader>
        <Divider />
        <CardBody className="space-y-6">
          <div>
            <div className="mb-2 flex items-center justify-between">
              <label className="block text-sm font-medium">Prompt système</label>
              <Tooltip content="Optimisez le prompt avec l'IA">
                <OptimizePromptButton onKeep={setSelectedPrompt} currentPrompt={selectedPrompt} />
              </Tooltip>
            </div>
            <Textarea
              value={selectedPrompt}
              onChange={(e) => setSelectedPrompt(e.target.value)}
              placeholder="Écrivez ici les instructions détaillées pour l'agent IA"
              variant="bordered"
              description="Définit précisément la mission, le rôle et les limites de l'agent"
              minRows={6}
              className="font-mono text-sm"
            />
          </div>
        </CardBody>
      </Card>

      {/* Bouton de sauvegarde fixe pour desktop */}
      <div className="hidden justify-end md:flex">
        <Button
          onPress={handleSaveChanges}
          size="lg"
          color="primary"
          isLoading={updateAgent.isPending}
          startContent={<Save />}
        >
          Sauvegarder les modifications
        </Button>
      </div>
    </div>
  )
}

export default AgentConfigAdmin
