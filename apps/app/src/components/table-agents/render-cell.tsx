"use client"

import React, { useState } from "react"
import Link from "next/link"
import { useTheme } from "next-themes"
import { <PERSON>, Pencil, Trash } from "lucide-react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { loadBadgeColorsFromDB } from "@/lib/utils/color-utils"
import { logger } from "@coheadcoaching/lib"
import { Button } from "@nextui-org/button"
import { Chip } from "@nextui-org/chip"
import { Modal, ModalBody, ModalContent, ModalFooter, ModalHeader } from "@nextui-org/modal"
import { Tooltip } from "@nextui-org/tooltip"
import { Prisma } from "@prisma/client"

type AgentWithBadge = Prisma.AgentGetPayload<{ include: { badge: true } }>

interface Props {
  agent: AgentWithBadge
  columnKey: string | React.Key
  onAgentDeleted?: () => void
}

export const RenderCell = ({ agent, columnKey, onAgentDeleted }: Props) => {
  const { theme } = useTheme()
  //@ts-expect-error Any type detected
  const cellValue = agent[columnKey]

  // Get badge colors for rendering
  const badgeColors = agent.badge?.primaryColor
    ? loadBadgeColorsFromDB({
      primaryColor: agent.badge.primaryColor,
      backgroundLight: agent.badge.backgroundLight,
      textLight: agent.badge.textLight,
      backgroundDark: agent.badge.backgroundDark,
      textDark: agent.badge.textDark,
    })
    : null

  switch (columnKey) {
    case "name":
      return (
        <div>
          {agent.icon + " "}
          {agent.title}
        </div>
      )
    case "description":
      return (
        <div>
          <Tooltip content={agent.description}>
            <span>
              {agent.description.slice(0, 200)}
              {agent.description.length > 200 && "..."}
            </span>
          </Tooltip>
        </div>
      )
    case "badge[title]":
      return agent.badge ? (
        badgeColors ? (
          <span
            className="inline-block w-fit rounded-full px-3 py-1 text-xs font-semibold"
            style={{
              backgroundColor: `hsl(${theme === 'dark' ? badgeColors['100-dark'] : badgeColors['100-light']})`,
              color: `hsl(${theme === 'dark' ? badgeColors['dark'] : badgeColors['light']})`
            }}
          >
            {agent.badge.title}
          </span>
        ) : (
          <Chip size="sm" variant="flat" color="warning">
            <span className="text-xs capitalize">{agent.badge.title}</span>
          </Chip>
        )
      ) : (
        <span className="text-xs text-default-400">Aucun badge</span>
      )

    case "actions":
      return (
        <div className="flex items-center gap-4">
          <div>
            <Tooltip content="Details">
              <Link href={`/admin/agents/${agent.id}/view`}>
                <Eye size={20} className="text-[#979797]" />
              </Link>
            </Tooltip>
          </div>
          <div>
            <Tooltip content="Modifier l'agent" color="secondary">
              <Link href={`/admin/agents/${agent.id}/edit`}>
                <Pencil size={20} className="text-[#979797]" />
              </Link>
            </Tooltip>
          </div>
          <div>
            <DeleteAgentButton agentId={agent.id} agentName={agent.title} onAgentDeleted={onAgentDeleted} />
          </div>
        </div>
      )
    default:
      return cellValue
  }
}

interface DeleteAgentButtonProps {
  agentId: number
  agentName: string
  onAgentDeleted?: () => void
}

const DeleteAgentButton = ({ agentId, onAgentDeleted, agentName }: DeleteAgentButtonProps) => {
  const utils = trpc.useUtils()
  const agentDelete = trpc.agent.delete.useMutation({
    onSuccess: () => {
      setShowModal(false)
      toast.success("Agent supprimé avec succès!")
      utils.agent.getAllForAdmin.invalidate()
      if (onAgentDeleted) {
        onAgentDeleted()
      }
    },

    onError: (error) => {
      logger.log(error)
      toast.error("Erreur lors de la suppression de l'agent")
    },
  })

  const [showModal, setShowModal] = useState(false)

  const handleOpenModal = () => {
    setShowModal(true)
  }

  const handleCloseModal = () => {
    setShowModal(false)
  }

  const handleAgentDelete = () => {
    agentDelete.mutate(String(agentId))
  }

  return (
    <>
      <Tooltip content="Delete agent" color="danger">
        <button onClick={handleOpenModal}>
          <Trash size={20} className="text-danger" />
        </button>
      </Tooltip>

      <Modal isOpen={showModal} onClose={handleCloseModal}>
        <ModalContent className="max-h-[90vh] lg:w-2/3">
          <ModalHeader>
            <h2 className="text-lg font-bold">Confirmation de suppression</h2>
          </ModalHeader>
          <ModalBody className="max-h-[80%] overflow-y-auto">
            Êtes-vous sûr de vouloir supprimer l&apos;agent {agentName} ?
          </ModalBody>
          <ModalFooter className="flex w-full justify-between">
            <Button onPress={handleCloseModal}>Annuler</Button>
            <Button isLoading={agentDelete.isPending} onPress={handleAgentDelete} variant="solid" color="danger">
              Supprimer
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}
