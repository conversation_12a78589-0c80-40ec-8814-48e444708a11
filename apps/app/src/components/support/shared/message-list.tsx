"use client"

import { useEffect, useRef } from "react"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { useSession } from "next-auth/react"
import { format } from "date-fns"
import { Download, Eye, Paperclip } from "lucide-react"
import { toast } from "react-toastify"

import { getImageUrl } from "@/lib/utils/client-utils"
import { logger } from "@coheadcoaching/lib"
import { Avatar } from "@nextui-org/avatar"
import { Button } from "@nextui-org/button"
import { File } from "@prisma/client"

interface Message {
  id: string
  content: string
  createdAt: Date
  sender: {
    id: string
    name?: string | null
    role: string
    roles: string[]
    profilePicture?: {
      id: string
      bucket: string
      endpoint: string
      key: string
      filetype: string
      createdAt: Date
      updatedAt: Date
      fileUploadingId: string | null
    } | null
  }
  attachment?: File | null
}

interface MessageListProps {
  messages: Message[]
  onImageView: (url: string, filename: string) => void
  showSenderName?: boolean
}

export default function MessageList({ messages, onImageView, showSenderName = false }: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const previousMessagesRef = useRef(messages)

  const session = useSession()
  const userId = session?.data?.user?.id
  const pathName = usePathname()

  useEffect(() => {
    if (!messages) return

    if (messages.length > previousMessagesRef.current.length) {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
    }

    previousMessagesRef.current = messages
  }, [messages])

  const handleDownloadFile = (url: string, filename: string) => {
    fetch(url)
      .then((response) => response.blob())
      .then((blob) => {
        const blobUrl = URL.createObjectURL(blob)

        const link = document.createElement("a")
        link.href = blobUrl
        link.download = filename
        document.body.appendChild(link)
        link.click()

        document.body.removeChild(link)
        setTimeout(() => URL.revokeObjectURL(blobUrl), 100)
      })
      .catch((error) => {
        logger.error("Erreur lors du téléchargement:", error)
        toast.error("Échec du téléchargement du fichier")
      })
  }

  return (
    <div className="mb-4 flex max-h-[500px] flex-col gap-4 overflow-y-auto overflow-x-hidden rounded-lg bg-default-50 p-3">
      {messages.map((msg) => {
        const isCurrentUser = msg.sender.id === userId
        const filename = msg.attachment?.key.split("/").pop() || "fichier"
        const isAdminInterface = pathName.includes("/admin")
        const isAdmin = msg.sender.roles.filter((role) => role !== "USER").length > 0

        return (
          <div key={msg.id} className={`flex max-w-full ${isCurrentUser ? "justify-end" : "justify-start"}`}>
            <div className={`flex gap-2 ${isCurrentUser ? "flex-row-reverse" : "flex-row"} max-w-[80%]`}>
              <Avatar
                className="size-8 min-w-8 shrink-0"
                src={
                  /* We don't want to show the logo in the admin interface when the user is admin  */
                  isAdminInterface || !isAdmin
                    ? msg.sender.profilePicture
                      ? getImageUrl(msg.sender.profilePicture)!
                      : undefined
                    : "/logo.svg"
                }
                name={!isAdmin ? msg.sender.name || undefined : "Cohead Support"}
                showFallback
              />
              <div>
                <div
                  className={`rounded-lg px-4 py-2 shadow-sm ${
                    isCurrentUser ? "bg-primary text-primary-foreground" : "bg-default-100"
                  }`}
                >
                  {showSenderName && (
                    <div className="mb-1 flex items-center gap-1 text-xs font-medium">{msg.sender.name || "User"}</div>
                  )}
                  <p className="whitespace-pre-wrap break-all">{msg.content}</p>
                  {msg.attachment && (
                    <div className="mt-2">
                      {msg.attachment.filetype.startsWith("image/") ? (
                        <div className="relative">
                          <div className="group relative">
                            <Image
                              width={300}
                              height={300}
                              src={getImageUrl(msg.attachment)!}
                              alt="Attachment"
                              className="max-h-64 w-auto max-w-full rounded object-contain transition-all hover:brightness-90"
                            />
                            <div className="absolute inset-0 flex items-center justify-center gap-2 opacity-0 transition-opacity group-hover:opacity-100">
                              <Button
                                isIconOnly
                                size="sm"
                                variant="flat"
                                className="bg-black/30 text-white"
                                onPress={() => onImageView(getImageUrl(msg.attachment)!, filename)}
                              >
                                <Eye size={16} />
                              </Button>
                              <Button
                                isIconOnly
                                size="sm"
                                variant="flat"
                                className="bg-black/30 text-white"
                                onPress={() => handleDownloadFile(getImageUrl(msg.attachment)!, filename)}
                              >
                                <Download size={16} />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2 rounded bg-default-200/50 p-2">
                          <Paperclip size={16} />
                          <span className="flex-1 truncate text-sm">{filename}</span>
                          <Button
                            isIconOnly
                            size="sm"
                            variant="flat"
                            onPress={() => handleDownloadFile(getImageUrl(msg.attachment)!, filename)}
                          >
                            <Download size={14} />
                          </Button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
                <div className={`mt-1 text-xs text-default-500 ${isCurrentUser ? "text-right" : "text-left"}`}>
                  {format(new Date(msg.createdAt), "HH:mm")}
                </div>
              </div>
            </div>
          </div>
        )
      })}
      <div ref={messagesEndRef} />
    </div>
  )
}
