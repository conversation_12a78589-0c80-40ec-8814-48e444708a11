import React from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { Check, X } from "lucide-react"

import { Button } from "@nextui-org/button"
import { Chip } from "@nextui-org/chip"

export interface Feature {
  text: string
  included: boolean
}

export interface Plan {
  id: number
  title: string
  price: number
  annualPrice: number
  features: Feature[]
  originalPrice?: number
  discountPercent?: number
  isRecommended?: boolean
  mainPeriod?: "MONTHLY" | "ANNUAL"
  freeTrialDays?: number | null
}

interface PricingCardProps {
  plan: Plan
}

const PricingCard: React.FC<PricingCardProps> = ({ plan }) => {
  const session = useSession()
  const userSub = session?.data?.user.lastSubscription
  const isCurrentUserSubscribedToPlan =
    userSub?.plan.id === plan.id && userSub?.billingPeriod === plan.mainPeriod && userSub.status === "ACTIVE"

  const router = useRouter()
  const {
    id,
    title,
    price,
    annualPrice,
    features,
    originalPrice,
    discountPercent,
    isRecommended = false,
    mainPeriod = "MONTHLY",
    freeTrialDays,
  } = plan

  // Determine which price to show as main price based on mainPeriod
  const mainPrice = mainPeriod === "MONTHLY" ? price : annualPrice
  const secondaryPrice = mainPeriod === "MONTHLY" ? annualPrice : price

  // Calculate original price if applicable
  const mainOriginalPrice =
    originalPrice && mainPeriod === "MONTHLY"
      ? originalPrice
      : mainPeriod === "ANNUAL" && originalPrice
        ? Math.round(originalPrice * 12 * 0.84 * 100) / 100
        : undefined

  const showDiscount = mainOriginalPrice !== undefined && discountPercent !== undefined && mainOriginalPrice > mainPrice

  const mainPeriodText = mainPeriod === "MONTHLY" ? "MOIS" : "AN"
  const secondaryPeriodText = mainPeriod === "MONTHLY" ? "AN" : "MOIS"

  const priceFormat = new Intl.NumberFormat("fr-FR", { style: "currency", currency: "EUR" })

  const handleChoosePlan = () => {
    router.push(`/subscription/checkout?planId=${id}&period=${mainPeriod}`)
  }

  return (
    <div className="relative flex w-64 flex-col overflow-hidden rounded-lg bg-white shadow-lg dark:border dark:border-gray-700/60 dark:bg-gray-800/70 dark:backdrop-blur-lg">
      {/* Recommended Badge */}
      {isRecommended && (
        <Chip
          color="primary"
          variant="solid"
          size="sm"
          className="absolute right-3 top-3 z-10 font-semibold uppercase tracking-wide"
        >
          Recommandé
        </Chip>
      )}

      {/* Header with clip path */}
      <div
        className="relative bg-primary-600 dark:bg-gray-700/80"
        style={{
          clipPath: "polygon(0 0, 100% 0, 100% 65%, 0 100%)",
          paddingBottom: "40px",
        }}
      >
        <div className="relative z-0 p-6 pb-3">
          <p className="text-sm font-semibold uppercase tracking-wide text-white dark:text-default-700">{title}</p>
          <div className="mt-2">
            {/* Discount Information */}
            {showDiscount && (
              <div className="mb-1 flex items-center space-x-2">
                <span className="text-sm text-white/80 line-through dark:text-default-500">
                  {priceFormat.format(mainOriginalPrice)}
                </span>
                <span className="rounded bg-white/20 px-1.5 py-0.5 text-xs font-semibold text-white dark:bg-default-600 dark:text-default-200">
                  -{discountPercent}%
                </span>
              </div>
            )}

            {/* Main Price */}
            <div className="flex items-baseline">
              <span className="relative bottom-2 pb-2 text-4xl font-bold text-white dark:text-primary-500">
                {priceFormat.format(mainPrice)}
              </span>
              {mainPeriodText && (
                <>
                  <span className="origin-bottom scale-125 text-large text-white dark:text-primary-500">/</span>
                  <span className="text-tiny text-white dark:text-primary-500">{mainPeriodText}</span>
                </>
              )}
            </div>

            {/* Optional Secondary Price */}
            {secondaryPrice !== undefined && (
              <p className="mt-1 text-xs text-white/90">
                ou {priceFormat.format(secondaryPrice)}/{secondaryPeriodText.toLowerCase()}
              </p>
            )}

            {/* Free Trial Badge */}
            {freeTrialDays && freeTrialDays > 0 && (
              <div className="mt-2">
                <Chip color="success" variant="flat" size="sm" className="bg-white/20 font-semibold text-white">
                  🎁 {freeTrialDays} jour{freeTrialDays > 1 ? "s" : ""} gratuit{freeTrialDays > 1 ? "s" : ""}
                </Chip>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Features list */}
      <div className="grow p-6 py-4">
        <ul className="space-y-3">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center">
              {feature.included ? (
                <Check className="mr-2 size-4 shrink-0 text-success-500 dark:text-success-400" />
              ) : (
                <X className="mr-2 size-4 shrink-0 text-danger-500 dark:text-danger-400" />
              )}
              <span className={`text-sm ${feature.included ? "text-default-700" : "text-default-500 line-through"}`}>
                {feature.text}
              </span>
            </li>
          ))}
        </ul>
      </div>

      {/* Button */}
      <div className="px-6 pb-6">
        <Button
          className="text-md w-full bg-primary-600 font-bold uppercase text-white"
          color="primary"
          variant="solid"
          onPress={handleChoosePlan}
          isDisabled={isCurrentUserSubscribedToPlan}
        >
          {isCurrentUserSubscribedToPlan ? "Plan Actuel" : "Choisir le plan"}
        </Button>
      </div>
    </div>
  )
}

export default PricingCard
