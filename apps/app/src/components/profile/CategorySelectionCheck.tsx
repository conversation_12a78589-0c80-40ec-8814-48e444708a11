"use client"

import { useEffect, useRef, useState } from "react"
import { useSession } from "next-auth/react"

import { useCategorySelectionModal } from "@/hooks/useCategorySelectionModal"
import { trpc } from "@/lib/trpc/client"

interface CategorySelectionCheckProps {
  forceSelection?: boolean
  onSelectionComplete?: () => void
}

const CategorySelectionCheck: React.FC<CategorySelectionCheckProps> = ({
  forceSelection = false,
  onSelectionComplete,
}) => {
  const { data: session } = useSession()
  const [hasCheckedSelection, setHasCheckedSelection] = useState(false)
  const modalOpenedRef = useRef(false)
  const selectionCompletedRef = useRef(false)

  const { openCategorySelectionModal, closeCategorySelectionModal, CategorySelectionModal } = useCategorySelectionModal(
    {
      forceSelection,
      onSuccess: () => {
        selectionCompletedRef.current = true
        closeCategorySelectionModal()
        onSelectionComplete?.()
      },
      title: "Personnalisez votre expérience",
      description: "Sélectionnez les catégories qui vous intéressent pour découvrir des agents adaptés à vos besoins",
    }
  )

  // Récupérer les données nécessaires
  const { data: userSelections, isSuccess: userSelectionsLoaded } = trpc.badge.getUserCategorySelections.useQuery(
    undefined,
    {
      enabled: !!session?.user,
      staleTime: 30000,
      gcTime: 60000,
    }
  )
  const { data: categoriesCount = 0, isSuccess: categoriesCountLoaded } = trpc.badge.getActiveCount.useQuery(
    undefined,
    {
      enabled: !!session?.user,
      staleTime: 30000,
      gcTime: 60000,
    }
  )
  const { data: userPlan, isSuccess: userPlanLoaded } = trpc.subscription.getUserActivePlan.useQuery(undefined, {
    enabled: !!session?.user,
    staleTime: 30000,
    gcTime: 60000,
  })

  // Vérifier s'il reste des catégories disponibles à sélectionner
  const selectedCount = userSelections?.categoryIds.length || 0
  const hasMoreBadgesToSelect = categoriesCount > selectedCount

  useEffect(() => {
    if (selectionCompletedRef.current) return

    if (
      !hasCheckedSelection &&
      userSelectionsLoaded &&
      userPlanLoaded &&
      categoriesCountLoaded &&
      !modalOpenedRef.current
    ) {
      setHasCheckedSelection(true)

      const categoryLimit = userPlan?.restrictions?.find((r) => r.type === "MAX_CATEGORIES")?.value ?? null
      const hasUnlimitedCategories = categoryLimit === null

      // Conditions principales
      const hasNoSelections = selectedCount === 0 && !hasUnlimitedCategories
      const hasTooManySelections =
        !hasUnlimitedCategories && typeof categoryLimit === "number" && selectedCount > categoryLimit
      const hasPlanChanged =
        !hasUnlimitedCategories && typeof categoryLimit === "number" && selectedCount !== categoryLimit

      // Ne pas ouvrir le modal s'il n'y a aucune catégorie disponible
      const shouldOpenForNoSelections = hasNoSelections && hasMoreBadgesToSelect
      const shouldOpenForPlanChange = hasPlanChanged && hasMoreBadgesToSelect

      const needsSelection = shouldOpenForNoSelections || hasTooManySelections || shouldOpenForPlanChange

      // Ouvrir le modal seulement s'il y a une raison valide
      if ((needsSelection || forceSelection) && !modalOpenedRef.current) {
        modalOpenedRef.current = true
        setTimeout(() => {
          if (hasMoreBadgesToSelect || hasTooManySelections) {
            openCategorySelectionModal()
          }
        }, 0)
      }
    }
  }, [
    hasCheckedSelection,
    userSelectionsLoaded,
    categoriesCountLoaded,
    userPlanLoaded,
    selectedCount,
    userPlan,
    forceSelection,
    openCategorySelectionModal,
    hasMoreBadgesToSelect,
  ])

  return <CategorySelectionModal />
}

export default CategorySelectionCheck
