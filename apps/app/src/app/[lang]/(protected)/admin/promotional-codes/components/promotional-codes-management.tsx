"use client"

import { useState } from "react"
import { Edit, Eye, Plus, Search, Trash2 } from "lucide-react"

import { trpc } from "@/lib/trpc/client"
import { logger } from "@coheadcoaching/lib"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { Input } from "@nextui-org/input"
import { Pagination } from "@nextui-org/pagination"
import { Select, SelectItem } from "@nextui-org/select"
import { Spinner } from "@nextui-org/spinner"
import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@nextui-org/table"

import CreatePromotionalCodeModal from "./create-promotional-code-modal"
import DeletePromCodeModal from "./delete-promotional-code-modal"
import EditPromotionalCodeModal from "./edit-promotional-code-modal"
import ViewPromotionalCodeModal from "./view-promotional-code-modal"

export default function PromotionalCodesManagement() {
  const [page, setPage] = useState(1)
  const [search, setSearch] = useState("")
  const [isActiveFilter, setIsActiveFilter] = useState<boolean | undefined>(undefined)
  const [effectTypeFilter, setEffectTypeFilter] = useState<string | undefined>(undefined)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showViewModal, setShowViewModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [selectedCodeId, setSelectedCodeId] = useState<string | null>(null)

  const limit = 10

  const {
    data: codesData,
    isLoading,
    error,
    refetch,
  } = trpc.promotionalCode.list.useQuery({
    page,
    limit,
    search: search || undefined,
    isActive: isActiveFilter,
    effectType: effectTypeFilter as "MONTHLY_ONLY" | "ANNUAL_ONLY" | "BOTH" | undefined,
  })

  const deleteCodeMutation = trpc.promotionalCode.delete.useMutation({
    onSuccess: () => {
      refetch()
    },
  })

  const handleDelete = (id: string) => {
    setSelectedCodeId(id)
    setShowDeleteModal(true)
  }

  const handleEdit = (id: string) => {
    setSelectedCodeId(id)
    setShowEditModal(true)
  }

  const handleView = (id: string) => {
    setSelectedCodeId(id)
    setShowViewModal(true)
  }

  const getStatusChip = (isActive: boolean) => {
    return (
      <Chip color={isActive ? "success" : "danger"} variant="flat" size="sm">
        {isActive ? "Actif" : "Inactif"}
      </Chip>
    )
  }

  const getEffectTypeChip = (effectType: string) => {
    const colors = {
      MONTHLY_ONLY: "primary",
      ANNUAL_ONLY: "secondary",
      BOTH: "success",
    } as const

    const labels = {
      MONTHLY_ONLY: "Mensuel",
      ANNUAL_ONLY: "Annuel",
      BOTH: "Les deux",
    }

    return (
      <Chip color={colors[effectType as keyof typeof colors]} variant="flat" size="sm">
        {labels[effectType as keyof typeof labels]}
      </Chip>
    )
  }

  const getDiscountDisplay = (discountType: string, discountValue: number) => {
    return discountType === "PERCENTAGE" ? `${discountValue}%` : `${discountValue / 100}€`
  }

  if (error) {
    return (
      <Card>
        <CardBody>
          <p className="text-danger">Erreur lors du chargement des codes promotionnels</p>
        </CardBody>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader className="flex items-center justify-between">
          <div className="flex flex-1 items-center gap-4">
            <Input
              placeholder="Rechercher par code, nom ou description..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              startContent={<Search className="size-4" />}
              className="max-w-md"
            />
            <Select
              label="Statut"
              placeholder="Tous les statuts"
              className="max-w-xs"
              selectedKeys={isActiveFilter !== undefined ? [isActiveFilter.toString()] : []}
              onSelectionChange={(keys) => {
                const value = Array.from(keys)[0] as string
                setIsActiveFilter(value === "true" ? true : value === "false" ? false : undefined)
              }}
              aria-label="Filtrer par statut"
            >
              <SelectItem key="true">Actif</SelectItem>
              <SelectItem key="false">Inactif</SelectItem>
            </Select>
            <Select
              label="Type d'effet"
              placeholder="Tous les types"
              className="max-w-xs"
              selectedKeys={effectTypeFilter ? [effectTypeFilter] : []}
              onSelectionChange={(keys) => {
                const value = Array.from(keys)[0] as string
                setEffectTypeFilter(value || undefined)
              }}
              aria-label="Filtrer par type d'effet"
            >
              <SelectItem key="MONTHLY_ONLY">Mensuel uniquement</SelectItem>
              <SelectItem key="ANNUAL_ONLY">Annuel uniquement</SelectItem>
              <SelectItem key="BOTH">Les deux</SelectItem>
            </Select>
          </div>
          <Button color="primary" startContent={<Plus className="size-4" />} onPress={() => setShowCreateModal(true)}>
            Créer un code
          </Button>
        </CardHeader>
        <CardBody>
          {isLoading ? (
            <div className="flex justify-center p-8">
              <Spinner size="lg" />
            </div>
          ) : codesData?.codes.length === 0 ? (
            <div className="p-8 text-center">
              <p className="text-default">Aucun code promotionnel trouvé</p>
            </div>
          ) : (
            <>
              <Table aria-label="Codes promotionnels">
                <TableHeader>
                  <TableColumn>CODE</TableColumn>
                  <TableColumn>NOM</TableColumn>
                  <TableColumn>REMISE</TableColumn>
                  <TableColumn>TYPE</TableColumn>
                  <TableColumn>UTILISATION</TableColumn>
                  <TableColumn>STATUT</TableColumn>
                  <TableColumn>ACTIONS</TableColumn>
                </TableHeader>
                <TableBody>
                  {codesData?.codes.map((code) => (
                    <TableRow key={code.id}>
                      <TableCell>
                        <code className="rounded bg-default-100 px-2 py-1 text-sm">{code.code}</code>
                      </TableCell>
                      <TableCell>{code.name}</TableCell>
                      <TableCell>{getDiscountDisplay(code.discountType, code.discountValue)}</TableCell>
                      <TableCell>{getEffectTypeChip(code.effectType)}</TableCell>
                      <TableCell>
                        {code.currentUsageCount}
                        {code.maxUsageCount && ` / ${code.maxUsageCount}`}
                      </TableCell>
                      <TableCell>{getStatusChip(code.isActive)}</TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                            onPress={() => handleView(code.id)}
                            aria-label={`Voir les détails du code ${code.code}`}
                          >
                            <Eye className="size-4" />
                          </Button>
                          <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                            onPress={() => handleEdit(code.id)}
                            aria-label={`Modifier le code ${code.code}`}
                          >
                            <Edit className="size-4" />
                          </Button>
                          <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                            color="danger"
                            onPress={() => handleDelete(code.id)}
                            aria-label={`Supprimer le code ${code.code}`}
                            // isLoading={deleteCodeMutation.isPending}
                          >
                            <Trash2 className="size-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  )) || []}
                </TableBody>
              </Table>

              {codesData && codesData.totalPages > 1 && (
                <div className="mt-4 flex justify-center">
                  <Pagination total={codesData.totalPages} page={page} onChange={setPage} showControls showShadow />
                </div>
              )}
            </>
          )}
        </CardBody>
      </Card>

      <CreatePromotionalCodeModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={() => {
          setShowCreateModal(false)
          refetch()
        }}
      />

      {selectedCodeId && (
        <>
          <EditPromotionalCodeModal
            isOpen={showEditModal}
            onClose={() => {
              setShowEditModal(false)
              setSelectedCodeId(null)
            }}
            codeId={selectedCodeId}
            onSuccess={() => {
              setShowEditModal(false)
              setSelectedCodeId(null)
              refetch()
            }}
          />

          <ViewPromotionalCodeModal
            isOpen={showViewModal}
            onClose={() => {
              setShowViewModal(false)
              setSelectedCodeId(null)
            }}
            codeId={selectedCodeId}
          />

          <DeletePromCodeModal
            isOpen={showDeleteModal}
            onClose={() => setShowDeleteModal(false)}
            isLoading={deleteCodeMutation.isPending}
            promCode={codesData?.codes.find((code) => code.id === selectedCodeId)?.code || ""}
            onConfirm={async () => {
              if (!selectedCodeId) return

              try {
                await deleteCodeMutation.mutateAsync({ id: selectedCodeId })
                setShowDeleteModal(false)
                setSelectedCodeId(null)
                refetch()
              } catch (err) {
                logger.error("Erreur lors de la suppression du code :", err)
              }
            }}
          />
        </>
      )}
    </>
  )
}
