import React from "react"

import { <PERSON><PERSON> } from "@nextui-org/button"
import { <PERSON><PERSON>, ModalBody, ModalContent, Modal<PERSON>ooter, ModalHeader } from "@nextui-org/modal"

interface DeletePromCodeModalProps {
  isOpen: boolean
  onConfirm: () => Promise<void>
  onClose: () => void
  promCode: string
  isLoading?: boolean
}

const DeletePromCodeModal = ({ isOpen, onConfirm, onClose, promCode, isLoading = false }: DeletePromCodeModalProps) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalContent>
        <ModalHeader>
          <h2 className="text-xl font-semibold">Supprimer le code promotionnel</h2>
        </ModalHeader>
        <ModalBody>
          <p>
            Êtes-vous sûr de vouloir supprimer le code promotionnel <strong>{promCode}</strong> ?
          </p>
        </ModalBody>
        <ModalFooter>
          <Button variant="light" color="default" onPress={onClose} isDisabled={isLoading}>
            Annuler
          </Button>
          <Button color="danger" onPress={onConfirm} isLoading={isLoading}>
            Supprimer
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}

export default DeletePromCodeModal
