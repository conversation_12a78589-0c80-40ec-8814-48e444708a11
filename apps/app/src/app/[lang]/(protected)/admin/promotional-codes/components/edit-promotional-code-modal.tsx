"use client"

import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { z } from "zod"

import { updatePromotionalCodeSchemaWithValidation } from "@/api/promotional-code/schemas"
import { trpc } from "@/lib/trpc/client"
import { logger } from "@coheadcoaching/lib"
import { zodResolver } from "@hookform/resolvers/zod"
import { parseDate } from "@internationalized/date"
import { Button } from "@nextui-org/button"
import { Card, CardBody } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { DatePicker } from "@nextui-org/date-picker"
import { Input } from "@nextui-org/input"
import { Textarea } from "@nextui-org/input"
import { <PERSON>dal, ModalBody, ModalContent, ModalFooter, ModalHeader } from "@nextui-org/modal"
import { Select, SelectItem } from "@nextui-org/select"
import { Spinner } from "@nextui-org/spinner"
import { Switch } from "@nextui-org/switch"
import { PromotionalCodeDiscountType, PromotionalCodeEffectType } from "@prisma/client"

type UpdatePromotionalCodeForm = z.infer<typeof updatePromotionalCodeSchemaWithValidation>

interface EditPromotionalCodeModalProps {
  isOpen: boolean
  onClose: () => void
  codeId: string
  onSuccess: () => void
}

export default function EditPromotionalCodeModal({
  isOpen,
  onClose,
  codeId,
  onSuccess,
}: EditPromotionalCodeModalProps) {
  const [selectedPlans, setSelectedPlans] = useState<Set<string>>(new Set())

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<UpdatePromotionalCodeForm>({
    resolver: zodResolver(updatePromotionalCodeSchemaWithValidation),
  })

  const discountType = watch("discountType")

  const { data: codeData, isLoading: isLoadingCode } = trpc.promotionalCode.get.useQuery(
    { id: codeId },
    { enabled: isOpen && !!codeId }
  )

  const { data: plansData } = trpc.plan.getAll.useQuery()

  const updateCodeMutation = trpc.promotionalCode.update.useMutation({
    onSuccess: () => {
      onSuccess()
    },
    onError: (error) => {
      logger.error("Update error:", error)
    },
  })

  // Populate form when data is loaded
  useEffect(() => {
    if (codeData && isOpen) {
      reset({
        name: codeData.name,
        description: codeData.description || "",
        effectType: codeData.effectType,
        discountType: codeData.discountType,
        discountValue: codeData.discountValue,
        paymentCount: codeData.paymentCount,
        isActive: codeData.isActive,
        validFrom: codeData.validFrom || undefined,
        validUntil: codeData.validUntil || undefined,
        maxUsageCount: codeData.maxUsageCount || undefined,
      })

      // Set selected plans
      if (codeData.restrictedToPlans) {
        setSelectedPlans(new Set(codeData.restrictedToPlans.map((plan) => plan.id.toString())))
      } else {
        setSelectedPlans(new Set())
      }
    }
  }, [codeData, isOpen, reset])

  useEffect(() => {
    if (codeData) {
      setValue("id", codeData.id)
      setValue("name", codeData.name)
      setValue("description", codeData.description || "")
      setValue("effectType", codeData.effectType)
      setValue("discountType", codeData.discountType)
      setValue("discountValue", codeData.discountValue)
      setValue("paymentCount", codeData.paymentCount)
      setValue("isActive", codeData.isActive)
      setValue("validFrom", codeData.validFrom || undefined)
      setValue("validUntil", codeData.validUntil || undefined)
      setValue("maxUsageCount", codeData.maxUsageCount || undefined)

      const planIds = new Set(codeData.restrictedToPlans.map((plan) => plan.id.toString()))
      setSelectedPlans(planIds)
    }
  }, [codeData, setValue])

  const onSubmit = (data: UpdatePromotionalCodeForm) => {
    const restrictedToPlanIds = selectedPlans.size > 0 ? Array.from(selectedPlans).map((id) => parseInt(id)) : undefined

    updateCodeMutation.mutate({
      ...data,
      id: codeId,
      restrictedToPlanIds,
    })
  }

  const handleClose = () => {
    reset()
    setSelectedPlans(new Set())
    onClose()
  }

  if (isLoadingCode) {
    return (
      <Modal isOpen={isOpen} onClose={handleClose}>
        <ModalContent>
          <ModalBody className="flex items-center justify-center p-8">
            <Spinner size="lg" />
          </ModalBody>
        </ModalContent>
      </Modal>
    )
  }

  if (!codeData) {
    return null
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="2xl" scrollBehavior="inside">
      <ModalContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <ModalHeader>
            <div>
              <h2 className="text-xl font-semibold">Modifier le code promotionnel</h2>
              <p className="text-sm text-default">Code: {codeData.code}</p>
            </div>
          </ModalHeader>
          <ModalBody className="max-h-[70vh] space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Input label="Nom" {...register("name")} isInvalid={!!errors.name} errorMessage={errors.name?.message} />
              <div className="flex items-center gap-2">
                <Switch isSelected={watch("isActive")} onValueChange={(value) => setValue("isActive", value)}>
                  Code actif
                </Switch>
              </div>
            </div>

            <Textarea label="Description (optionnel)" {...register("description")} />

            <div className="grid grid-cols-2 gap-4">
              <Select
                label="Type d'effet"
                selectedKeys={watch("effectType") ? new Set([watch("effectType") as string]) : new Set()}
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as PromotionalCodeEffectType
                  setValue("effectType", value)
                }}
              >
                <SelectItem key="MONTHLY_ONLY">Mensuel uniquement</SelectItem>
                <SelectItem key="ANNUAL_ONLY">Annuel uniquement</SelectItem>
                <SelectItem key="BOTH">Les deux</SelectItem>
              </Select>

              <Select
                label="Type de remise"
                selectedKeys={watch("discountType") ? new Set([watch("discountType") as string]) : new Set()}
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as PromotionalCodeDiscountType
                  setValue("discountType", value)
                }}
              >
                <SelectItem key="PERCENTAGE">Pourcentage</SelectItem>
                <SelectItem key="FIXED_AMOUNT">Montant fixe</SelectItem>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <Input
                label={discountType === "PERCENTAGE" ? "Pourcentage de remise" : "Montant de remise (en centimes)"}
                type="number"
                {...register("discountValue", { valueAsNumber: true })}
                isInvalid={!!errors.discountValue}
                errorMessage={errors.discountValue?.message}
                endContent={discountType === "PERCENTAGE" ? "%" : "centimes"}
              />
              <Input
                label="Nombre de paiements"
                type="number"
                {...register("paymentCount", { valueAsNumber: true })}
                isInvalid={!!errors.paymentCount}
                errorMessage={errors.paymentCount?.message}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <DatePicker
                label="Date de début (optionnel)"
                // @ts-expect-error DateValue type compatibility issue with NextUI
                value={watch("validFrom") ? parseDate(new Date(watch("validFrom")!).toISOString().split("T")[0]) : null}
                onChange={(date) => setValue("validFrom", date?.toDate("UTC") || undefined, { shouldValidate: true })}
                isInvalid={!!errors.validFrom}
                errorMessage={errors.validFrom?.message}
              />
              <DatePicker
                label="Date de fin"
                // @ts-expect-error DateValue type compatibility issue with NextUI
                value={
                  watch("validUntil") ? parseDate(new Date(watch("validUntil")!).toISOString().split("T")[0]) : null
                }
                onChange={(date) => setValue("validUntil", date?.toDate("UTC") || undefined, { shouldValidate: true })}
                isInvalid={!!errors.validUntil}
                errorMessage={errors.validUntil?.message}
              />
            </div>

            <Input
              label="Limite d'utilisation"
              type="number"
              value={watch("maxUsageCount")?.toString() || ""}
              onChange={(e) => {
                const value = e.target.value
                setValue("maxUsageCount", value === "" ? undefined : parseInt(value) || undefined, {
                  shouldValidate: true,
                })
              }}
              isInvalid={!!errors.maxUsageCount}
              errorMessage={errors.maxUsageCount?.message}
              description="Au moins une date de fin OU une limite d'utilisation doit être spécifiée"
            />

            {plansData && plansData.length > 0 && (
              <Card className="shrink-0">
                <CardBody>
                  <h4 className="mb-2 font-medium">Plans autorisés</h4>
                  <div className="flex flex-wrap gap-2">
                    {plansData?.map((plan) => (
                      <Chip
                        key={plan.id}
                        variant={selectedPlans.has(plan.id.toString()) ? "solid" : "bordered"}
                        color={selectedPlans.has(plan.id.toString()) ? "primary" : "default"}
                        className="cursor-pointer"
                        onClick={() => {
                          const newSelected = new Set(selectedPlans)
                          if (newSelected.has(plan.id.toString())) {
                            newSelected.delete(plan.id.toString())
                          } else {
                            newSelected.add(plan.id.toString())
                          }
                          setSelectedPlans(newSelected)
                        }}
                      >
                        {plan.name}
                      </Chip>
                    ))}
                  </div>
                </CardBody>
              </Card>
            )}

            <Card className="shrink-0">
              <CardBody>
                <h4 className="mb-2 font-medium">Statistiques d&apos;utilisation</h4>
                <p className="text-sm text-default">
                  Utilisations actuelles: {codeData.currentUsageCount}
                  {codeData.maxUsageCount && ` / ${codeData.maxUsageCount}`}
                </p>
              </CardBody>
            </Card>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={handleClose}>
              Annuler
            </Button>
            <Button color="primary" type="submit" isLoading={updateCodeMutation.isPending}>
              Mettre à jour
            </Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  )
}
