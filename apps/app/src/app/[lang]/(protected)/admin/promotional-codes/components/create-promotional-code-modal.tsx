"use client"

import { useState } from "react"
import { addDays } from "date-fns"
import { useForm } from "react-hook-form"
import { z } from "zod"

import { createPromotionalCodeSchemaWithValidation } from "@/api/promotional-code/schemas"
import { trpc } from "@/lib/trpc/client"
import { zodResolver } from "@hookform/resolvers/zod"
import { getLocalTimeZone, parseDate, today } from "@internationalized/date"
import { But<PERSON> } from "@nextui-org/button"
import { Card, CardBody } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { DatePicker } from "@nextui-org/date-picker"
import { Input } from "@nextui-org/input"
import { Textarea } from "@nextui-org/input"
import { Modal, ModalBody, ModalContent, ModalFooter, ModalHeader } from "@nextui-org/modal"
import { Select, SelectItem } from "@nextui-org/select"

type CreatePromotionalCodeForm = z.infer<typeof createPromotionalCodeSchemaWithValidation>

interface CreatePromotionalCodeModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

export default function CreatePromotionalCodeModal({ isOpen, onClose, onSuccess }: CreatePromotionalCodeModalProps) {
  const [selectedPlans, setSelectedPlans] = useState<Set<string>>(new Set())

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<CreatePromotionalCodeForm>({
    resolver: zodResolver(createPromotionalCodeSchemaWithValidation),
    defaultValues: {
      code: "",
      name: "",
      description: "",
      effectType: "BOTH",
      discountType: "PERCENTAGE",
      discountValue: 0,
      paymentCount: 1,
      validFrom: undefined,
      validUntil: undefined,
      maxUsageCount: undefined,
      restrictedToPlanIds: [],
    },
  })

  const discountType = watch("discountType")

  const { data: plansData } = trpc.plan.getAll.useQuery()

  const createCodeMutation = trpc.promotionalCode.create.useMutation({
    onSuccess: () => {
      reset()
      setSelectedPlans(new Set())
      onSuccess()
    },
    onError: (error) => {
      console.error("Create error:", error)
    },
  })

  const onSubmit = (data: CreatePromotionalCodeForm) => {
    const restrictedToPlanIds = selectedPlans.size > 0 ? Array.from(selectedPlans).map((id) => parseInt(id)) : undefined

    createCodeMutation.mutate({
      ...data,
      code: data.code.toUpperCase(),
      restrictedToPlanIds,
    })
  }

  const handleClose = () => {
    reset()
    setSelectedPlans(new Set())
    onClose()
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="2xl" scrollBehavior="inside">
      <ModalContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <ModalHeader>
            <h2 className="text-xl font-semibold">Créer un code promotionnel</h2>
          </ModalHeader>
          <ModalBody className="max-h-[70vh] space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Input
                label="Code"
                placeholder="PROMO2024"
                {...register("code")}
                isInvalid={!!errors.code}
                errorMessage={errors.code?.message}
                description="Lettres majuscules, chiffres, tirets et underscores uniquement"
              />
              <Input
                label="Nom"
                placeholder="Promotion de Noël"
                {...register("name")}
                isInvalid={!!errors.name}
                errorMessage={errors.name?.message}
              />
            </div>

            <Textarea
              label="Description (optionnel)"
              placeholder="Description du code promotionnel..."
              {...register("description")}
            />

            <div className="grid grid-cols-2 gap-4">
              <Select label="Type d'effet" {...register("effectType")} defaultSelectedKeys={["BOTH"]}>
                <SelectItem key="MONTHLY_ONLY">Mensuel uniquement</SelectItem>
                <SelectItem key="ANNUAL_ONLY">Annuel uniquement</SelectItem>
                <SelectItem key="BOTH">Les deux</SelectItem>
              </Select>

              <Select
                label="Type de remise"
                {...register("discountType")}
                defaultSelectedKeys={["PERCENTAGE"]}
                onChange={(e) => setValue("discountType", e.target.value as "PERCENTAGE" | "FIXED_AMOUNT")}
              >
                <SelectItem key="PERCENTAGE">Pourcentage</SelectItem>
                <SelectItem key="FIXED_AMOUNT">Montant fixe</SelectItem>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <Input
                label={discountType === "PERCENTAGE" ? "Pourcentage de remise" : "Montant de remise (en centimes)"}
                type="number"
                placeholder={discountType === "PERCENTAGE" ? "10" : "500"}
                {...register("discountValue", { valueAsNumber: true })}
                isInvalid={!!errors.discountValue}
                errorMessage={errors.discountValue?.message}
                endContent={discountType === "PERCENTAGE" ? "%" : "centimes"}
              />
              <Input
                label="Nombre de paiements"
                type="number"
                placeholder="1"
                {...register("paymentCount", { valueAsNumber: true })}
                isInvalid={!!errors.paymentCount}
                errorMessage={errors.paymentCount?.message}
                description="Nombre de paiements auxquels la remise s'applique"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <DatePicker
                label="Date de début (optionnel)"
                // @ts-expect-error DateValue type compatibility issue with NextUI
                value={watch("validFrom") ? parseDate(new Date(watch("validFrom")!).toISOString().split("T")[0]) : null}
                onChange={(date) => setValue("validFrom", date?.toDate("UTC") || undefined, { shouldValidate: true })}
                isInvalid={!!errors.validFrom}
                // @ts-expect-error DateValue type compatibility issue with NextUI
                minValue={today(getLocalTimeZone())}
                errorMessage={errors.validFrom?.message}
              />
              <DatePicker
                label="Date de fin"
                // @ts-expect-error DateValue type compatibility issue with NextUI
                value={
                  watch("validUntil") ? parseDate(new Date(watch("validUntil")!).toISOString().split("T")[0]) : null
                }
                onChange={(date) => setValue("validUntil", date?.toDate("UTC") || undefined, { shouldValidate: true })}
                isInvalid={!!errors.validUntil}
                // @ts-expect-error DateValue type compatibility issue with NextUI
                minValue={
                  watch("validFrom")
                    ? parseDate(
                        addDays(new Date(watch("validFrom")!), 1)
                          .toISOString()
                          .split("T")[0]
                      )
                    : today(getLocalTimeZone())
                }
                errorMessage={errors.validUntil?.message}
              />
            </div>

            <Input
              label="Limite d'utilisation"
              type="number"
              placeholder="100"
              value={watch("maxUsageCount")?.toString() || ""}
              onChange={(e) => {
                const value = e.target.value
                setValue("maxUsageCount", value === "" ? undefined : parseInt(value) || undefined, {
                  shouldValidate: true,
                })
              }}
              isInvalid={!!errors.maxUsageCount}
              errorMessage={errors.maxUsageCount?.message}
              description="Au moins une date de fin OU une limite d'utilisation doit être spécifiée"
            />

            {plansData && plansData.length > 0 && (
              <Card className="shrink-0">
                <CardBody>
                  <h4 className="mb-2 font-medium">Plans autorisés (optionnel)</h4>
                  <p className="mb-3 text-sm text-default">
                    Si aucun plan n&apos;est sélectionné, le code sera valide pour tous les plans
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {plansData?.map((plan) => (
                      <Chip
                        key={plan.id}
                        variant={selectedPlans.has(plan.id.toString()) ? "solid" : "bordered"}
                        color={selectedPlans.has(plan.id.toString()) ? "primary" : "default"}
                        className="cursor-pointer"
                        onClick={() => {
                          const newSelected = new Set(selectedPlans)
                          if (newSelected.has(plan.id.toString())) {
                            newSelected.delete(plan.id.toString())
                          } else {
                            newSelected.add(plan.id.toString())
                          }
                          setSelectedPlans(newSelected)
                        }}
                      >
                        {plan.name}
                      </Chip>
                    ))}
                  </div>
                </CardBody>
              </Card>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={handleClose}>
              Annuler
            </Button>
            <Button color="primary" type="submit" isLoading={createCodeMutation.isPending}>
              Créer le code
            </Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  )
}
