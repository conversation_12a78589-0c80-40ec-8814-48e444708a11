import { Metadata } from "next"

import PromotionalCodesManagement from "./components/promotional-codes-management"

export const metadata: Metadata = {
  title: "CoheadCoaching | Gestion des codes promotionnels - Administration",
  description: "Interface d'administration pour gérer les codes promotionnels",
}

export default async function PromotionalCodesPage() {
  // Simplified dictionary for now
  const dictionary = {
    admin: {
      promotionalCodes: {
        title: "Codes Promotionnels",
        description: "Gérer les codes promotionnels et les remises",
        createNew: "Créer un Nouveau Code",
        searchPlaceholder: "Rechercher des codes...",
        noCodesFound: "Aucun code promotionnel trouvé",
        loading: "Chargement...",
        error: "Erreur lors du chargement des codes promotionnels",
      },
    },
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">{dictionary.admin.promotionalCodes.title}</h1>
        <p className="mt-2 text-default">{dictionary.admin.promotionalCodes.description}</p>
      </div>

      <PromotionalCodesManagement />
    </div>
  )
}
