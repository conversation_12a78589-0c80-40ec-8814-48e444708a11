import React, { Suspense } from "react"
import { redirect } from "next/navigation"
import { addDays } from "date-fns"
import { Calendar, CheckCircle2, Clock, Gift, Lock } from "lucide-react"

import { auth } from "@/lib/auth"
import { serverTrpc } from "@/lib/trpc/server"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { Divider } from "@nextui-org/divider"
import { Spinner } from "@nextui-org/spinner"

import CheckoutForm from "./components/checkout-form"

// Composant de chargement
function CheckoutLoading() {
  return (
    <div className="container mx-auto flex min-h-[calc(100vh-10rem)] max-w-2xl items-center justify-center px-4 py-20">
      <Card className="w-full p-8 text-center">
        <CardBody className="flex flex-col items-center gap-6">
          <Spinner size="lg" color="primary" />
          <h2 className="text-xl font-semibold">Chargement de votre paiement</h2>
          <p className="text-default-500">Veuillez patienter pendant que nous préparons votre commande...</p>
        </CardBody>
      </Card>
    </div>
  )
}

// Composant pour les vérifications et validation des données
async function CheckoutVerification({ searchParams }: { searchParams: { planId?: string; period?: string } }) {
  const planId = searchParams.planId
  const period = searchParams.period as "MONTHLY" | "ANNUAL"

  if (!planId || !period) {
    redirect("/pricing")
  }

  const planData = await serverTrpc.plan.getById(Number(planId))
  if (!planData) {
    redirect("/pricing")
  }

  const setupStatus = await serverTrpc.subscription.checkSetup()
  const session = await auth()

  if (!setupStatus.hasMangopayAccount || !setupStatus.hasCard) {
    redirect(`/subscription/setup?planId=${planId}&period=${period}`)
  }

  const price = period === "MONTHLY" ? planData.monthlyPrice : planData.annualPrice
  const periodText = period === "MONTHLY" ? "mois" : "an"
  const defaultCard =
    setupStatus.user?.cards?.findLast((card) => card.isDefault) ||
    setupStatus.user?.cards?.findLast((card) => card.mangopayCardId)

  if (!defaultCard?.mangopayCardId) {
    console.error("Aucune carte par défaut ou valide trouvée pour l'utilisateur.")
    redirect(`/subscription/setup?planId=${planId}&period=${period}&error=no_card`)
  }

  // Check free trial eligibility
  const hasUsedFreeTrial = session?.user?.hasUsedFreeTrial || false
  const planHasFreeTrial = planData.freeTrialDays && planData.freeTrialDays > 0
  const isEligibleForFreeTrial = planHasFreeTrial && !hasUsedFreeTrial

  // Calculate free trial information
  let freeTrialInfo = null
  if (isEligibleForFreeTrial && planData.freeTrialDays) {
    const trialEndDate = addDays(new Date(), planData.freeTrialDays)
    const reminderDate = addDays(trialEndDate, -2) // 2 days before trial ends

    freeTrialInfo = {
      days: planData.freeTrialDays,
      endDate: trialEndDate,
      reminderDate: reminderDate,
      isEligible: true,
    }
  }

  // Retourner les données validées pour le composant de contenu
  return {
    planData,
    setupStatus,
    price,
    periodText,
    defaultCard,
    planId: Number(planId),
    period,
    freeTrialInfo,
  }
}

// Composant principal avec la logique de checkout
async function CheckoutContent({ searchParams }: { searchParams: { planId?: string; period?: string } }) {
  const verifiedData = await CheckoutVerification({ searchParams })
  const { planData, price, periodText, defaultCard, planId, period, freeTrialInfo } = verifiedData

  return (
    <div className="container mx-auto max-w-3xl px-4 py-12 sm:px-6 lg:px-8">
      <h1 className="mb-8 text-center text-3xl font-bold">Finalisez votre abonnement</h1>

      <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
        {/* Colonne 1: Détails du plan */}
        <Card shadow="sm" className="border-none bg-background/60 dark:bg-default-100/50">
          <CardHeader className="flex-col items-start px-4 pb-0 pt-4">
            <p className="text-tiny font-bold uppercase text-default-500">Votre Sélection</p>
            <h2 className="mt-1 text-large font-bold">{planData.name}</h2>
          </CardHeader>
          <CardBody className="overflow-visible py-2">
            <p className="mb-4 text-sm text-default-500">{planData.description}</p>
            <div className="mb-4">
              {freeTrialInfo?.isEligible ? (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Chip color="success" variant="flat" size="sm" startContent={<Gift className="size-3" />}>
                      Essai gratuit de {freeTrialInfo.days} jour{freeTrialInfo.days > 1 ? "s" : ""}
                    </Chip>
                  </div>
                  <div className="text-2xl font-bold text-success">
                    Gratuit pendant {freeTrialInfo.days} jour{freeTrialInfo.days > 1 ? "s" : ""}
                  </div>
                  <div className="text-sm text-default-500">
                    Puis {Intl.NumberFormat("fr-FR", { style: "currency", currency: "EUR" }).format(price / 100)}/
                    {periodText}
                  </div>
                </div>
              ) : (
                <div>
                  <span className="text-3xl font-extrabold text-primary">
                    {Intl.NumberFormat("fr-FR", { style: "currency", currency: "EUR" }).format(price / 100)}
                  </span>
                  <span className="text-default-500">/{periodText}</span>
                </div>
              )}
            </div>

            {planData.features && planData.features.length > 0 && (
              <>
                <Divider className="my-4" />
                <p className="mb-3 text-sm font-semibold text-default-600">Fonctionnalités clés :</p>
                <ul className="space-y-2">
                  {planData.features.map(
                    (feature, index) =>
                      feature.included && (
                        <li key={index} className="flex items-center text-sm">
                          <CheckCircle2 className="mr-2 size-5 shrink-0 text-success" />
                          <span>{feature.text}</span>
                        </li>
                      )
                  )}
                </ul>
              </>
            )}

            {/* Free Trial Information */}
            {freeTrialInfo?.isEligible && (
              <>
                <Divider className="my-4" />
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Gift className="size-4 text-success" />
                    <p className="text-sm font-semibold text-success">Informations sur l&apos;essai gratuit</p>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <Calendar className="size-3 text-default-500" />
                      <span className="text-default-600">
                        Fin de l&apos;essai :{" "}
                        {freeTrialInfo.endDate.toLocaleDateString("fr-FR", {
                          weekday: "long",
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        })}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="size-3 text-default-500" />
                      <span className="text-default-600">
                        Rappel par email :{" "}
                        {freeTrialInfo.reminderDate.toLocaleDateString("fr-FR", {
                          weekday: "long",
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        })}
                      </span>
                    </div>
                    <div className="mt-2 rounded-lg bg-success-50 p-3 dark:bg-success-100/10">
                      <p className="text-xs text-success-700 dark:text-success-300">
                        💡 Vous pouvez annuler à tout moment pendant l&apos;essai gratuit sans frais. Votre abonnement
                        se renouvellera automatiquement à la fin de la période d&apos;essai.
                      </p>
                    </div>
                  </div>
                </div>
              </>
            )}
          </CardBody>
        </Card>

        {/* Colonne 2: Paiement */}
        <div className="space-y-6">
          <Card shadow="sm" className="border-none bg-background/60 dark:bg-default-100/50">
            <CardHeader>
              <h2 className="text-lg font-semibold">Confirmer le paiement</h2>
            </CardHeader>
            <CardBody>
              <p className="mb-4 text-sm text-default-600">
                Votre paiement sera traité de manière sécurisée via notre partenaire MangoPay.
              </p>
              <div className="mb-6 rounded-lg bg-default-100 p-4 dark:bg-default-200/50">
                <p className="mb-2 text-sm font-medium text-default-700">Payer avec :</p>
                <div className="flex items-center justify-between">
                  <span className="font-mono text-sm tracking-wider text-default-800">
                    Carte se terminant par {defaultCard.last4}
                  </span>
                  <span className="text-xs text-default-500">
                    Exp: {defaultCard.expirationDate?.slice(0, 2) + "/" + defaultCard.expirationDate?.slice(2, 4)}
                  </span>
                </div>
              </div>

              {/* Intégration du formulaire client */}
              <CheckoutForm
                planId={planId}
                period={period}
                cardId={defaultCard.id}
                mangopayCardId={defaultCard.mangopayCardId!}
                price={price}
                periodText={periodText}
              />
            </CardBody>
          </Card>
          <div className="flex items-center justify-center gap-2 text-center text-xs text-default-500">
            <Lock className="size-4 text-success" />
            <span>Transactions sécurisées</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function CheckoutPage({ searchParams }: { searchParams: { planId?: string; period?: string } }) {
  return (
    <Suspense fallback={<CheckoutLoading />}>
      <CheckoutContent searchParams={searchParams} />
    </Suspense>
  )
}
