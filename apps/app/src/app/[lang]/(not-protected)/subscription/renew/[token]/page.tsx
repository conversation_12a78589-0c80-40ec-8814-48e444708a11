import React, { Suspense } from "react"
import Link from "next/link"
import { AlertCircle, CheckCircle2, Clock } from "lucide-react"

import { serverTrpc } from "@/lib/trpc/server"
import { But<PERSON> } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Divider } from "@nextui-org/divider"
import { Spinner } from "@nextui-org/spinner"

import RenewalForm from "./components/renewal-form"

// Loading component
function RenewalLoading() {
  return (
    <div className="container mx-auto flex min-h-[calc(100vh-10rem)] max-w-2xl items-center justify-center px-4 py-20">
      <Card className="w-full p-8 text-center">
        <CardBody className="flex flex-col items-center gap-6">
          <Spinner size="lg" color="primary" />
          <h2 className="text-xl font-semibold">Vérification du lien de renouvellement</h2>
          <p className="text-default-500">Veuillez patienter pendant que nous validons votre demande...</p>
        </CardBody>
      </Card>
    </div>
  )
}

// Verification component
async function RenewalVerification({ token }: { token: string }) {
  try {
    const result = await serverTrpc.subscription.validateRenewalToken({ token })
    return result
  } catch (error) {
    return null
  }
}

// Main content component
async function RenewalContent({ token }: { token: string }) {
  const validationResult = await RenewalVerification({ token })

  if (!validationResult) {
    return (
      <div className="container mx-auto flex min-h-screen max-w-2xl items-center px-4 py-12">
        <Card className="border-danger-200 bg-danger-50">
          <CardBody className="flex flex-col items-center gap-6 p-8 text-center">
            <AlertCircle className="size-16 text-danger" />
            <h1 className="text-2xl font-bold text-danger">Lien invalide ou expiré</h1>
            <p className="text-danger-600">
              Ce lien de renouvellement n&apos;est plus valide. Il a peut-être expiré ou a déjà été utilisé.
            </p>
            <p className="text-sm text-danger-500">
              Veuillez contacter notre support si vous pensez qu&apos;il s&apos;agit d&apos;une erreur.
            </p>
            <Button color="primary" variant="bordered" as={Link} href="/">
              Retour à l&apos;accueil
            </Button>
          </CardBody>
        </Card>
      </div>
    )
  }

  const { subscription } = validationResult
  const price =
    subscription.billingPeriod === "MONTHLY" ? subscription.plan.monthlyPrice : subscription.plan.annualPrice
  const periodText = subscription.billingPeriod === "MONTHLY" ? "mois" : "an"
  const formattedPrice = Intl.NumberFormat("fr-FR", {
    style: "currency",
    currency: "EUR",
  }).format(price / 100)

  return (
    <div className="container mx-auto max-w-4xl px-4 py-12">
      <div className="mb-8 text-center">
        <h1 className="mb-4 text-3xl font-bold text-gray-800 dark:text-gray-100">Renouvellement de votre abonnement</h1>
        <p className="text-default-600">
          Confirmez le renouvellement de votre abonnement pour continuer à profiter de nos services.
        </p>
      </div>

      <div className="grid gap-8 lg:grid-cols-2">
        {/* Column 1: Subscription Details */}
        <div className="space-y-6">
          <Card shadow="sm" className="border-none bg-background/60 dark:bg-default-100/50">
            <CardHeader>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Détails de l&apos;abonnement</h2>
            </CardHeader>
            <CardBody className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-default-600">Plan :</span>
                <span className="font-medium">{subscription.plan.name}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-default-600">Période :</span>
                <span className="font-medium">{subscription.billingPeriod === "MONTHLY" ? "Mensuel" : "Annuel"}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-default-600">Prix :</span>
                <span className="text-lg font-bold text-primary">
                  {formattedPrice}/{periodText}
                </span>
              </div>
              <Divider />
              <div className="flex items-center justify-between">
                <span className="text-default-600">Utilisateur :</span>
                <span className="font-medium">{subscription.user.email}</span>
              </div>
            </CardBody>
          </Card>

          <Card shadow="sm" className="border-none bg-success-50 dark:bg-success-100/20">
            <CardBody className="flex flex-row items-center gap-4 p-4">
              <CheckCircle2 className="size-6 text-success" />
              <div>
                <p className="font-medium text-success-700 dark:text-success-600">Renouvellement sécurisé</p>
                <p className="text-sm text-success-600 dark:text-success-500">
                  Votre paiement sera traité de manière sécurisée via MangoPay
                </p>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Column 2: Renewal Form */}
        <div className="space-y-6">
          <Card shadow="sm" className="border-none bg-background/60 dark:bg-default-100/50">
            <CardHeader>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Confirmer le renouvellement</h2>
            </CardHeader>
            <CardBody>
              <div className="mb-6 rounded-lg bg-warning-50 p-4 dark:bg-warning-100/20">
                <div className="flex items-center gap-3">
                  <Clock className="size-5 text-warning" />
                  <div>
                    <p className="font-medium text-warning-700 dark:text-warning-600">Action requise</p>
                    <p className="text-sm text-warning-600 dark:text-warning-500">
                      Votre abonnement nécessite une re-authentification pour continuer.
                    </p>
                  </div>
                </div>
              </div>

              <RenewalForm token={token} price={price} periodText={periodText} />
            </CardBody>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default function RenewalPage({ params }: { params: { token: string } }) {
  return (
    <Suspense fallback={<RenewalLoading />}>
      <RenewalContent token={params.token} />
    </Suspense>
  )
}
