import NavSettings from "@/components/nav-settings"
import PricingToggleSection from "@/components/pricing/pricing-toggle-section"
import { Locale } from "@/lib/i18n-config"
import { serverTrpc } from "@/lib/trpc/server"

export default async function Home({
  params: { lang },
}: {
  params: {
    lang: Locale
  }
}) {
  const dbPlans = await serverTrpc.plan.getAll()

  // Transformer les plans de la base de données au format attendu par PricingCard
  const plans = dbPlans.map((dbPlan) => {
    let originalPrice
    let discountPercent

    /*if (dbPlan.isRecommended) {
            originalPrice = Math.round(dbPlan.monthlyPrice * 1.16) / 100;
            discountPercent = 16;
        }*/

    return {
      id: dbPlan.id,
      title: dbPlan.name,
      price: dbPlan.monthlyPrice / 100,
      annualPrice: dbPlan.annualPrice / 100,
      features: dbPlan.features,
      originalPrice,
      discountPercent,
      isRecommended: dbPlan.isRecommended,
      freeTrialDays: dbPlan.freeTrialDays,
    }
  })

  return (
    <main className="container m-auto flex min-h-screen flex-1 flex-col items-center gap-3">
      <NavSettings lang={lang} />
      <div className="w-full p-6">
        <div className="w-full max-w-[1200px] place-self-center">
          <h2 className="mb-2 text-3xl font-bold">Nos tarifs</h2>
          <p className="text-gray-400">
            Explorez l&apos;ensemble des tarifs disponibles sur notre plateforme et choisissez celui qui vous convient!
          </p>
        </div>
      </div>
      <div className="w-full p-6">
        <PricingToggleSection plans={plans} />
      </div>
    </main>
  )
}
