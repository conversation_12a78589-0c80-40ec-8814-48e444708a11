import { NextRequest, NextResponse } from "next/server"
import { createHmac } from "crypto"

import { sendFreeTrialReminderEmail } from "@/lib/email/free-trial-reminder"
import { env } from "@/lib/env"
import { logger } from "@coheadcoaching/lib"

interface FreeTrialReminderWebhookPayload {
  to: string
  userName: string
  planName: string
  trialEndDate: string
  manageSubscriptionUrl: string
  logoUrl: string
  timestamp: number
}

function verifySignature(payload: string, signature: string, secret: string): boolean {
  const expectedSignature = "sha256=" + createHmac("sha256", secret).update(payload).digest("hex")
  return signature === expectedSignature
}

export async function POST(request: NextRequest) {
  try {
    // Check API key
    const apiKey = request.headers.get("x-api-key")
    if (!apiKey || apiKey !== env.EMAIL_WEBHOOK_API_KEY) {
      logger.warn("Free trial reminder webhook: Invalid API key")
      return NextResponse.json({ success: false, error: "Unauthorized" }, { status: 401 })
    }

    // Get payload
    const body = await request.text()
    let payload: FreeTrialReminderWebhookPayload

    try {
      payload = JSON.parse(body) as FreeTrialReminderWebhookPayload
    } catch (error) {
      logger.error("Free trial reminder webhook: Invalid JSON payload", { error })
      return NextResponse.json({ success: false, error: "Invalid JSON" }, { status: 400 })
    }

    // Verify signature
    const signature = request.headers.get("x-signature")
    if (!signature || !verifySignature(body, signature, env.EMAIL_WEBHOOK_API_KEY)) {
      logger.warn("Free trial reminder webhook: Invalid signature")
      return NextResponse.json({ success: false, error: "Invalid signature" }, { status: 401 })
    }

    // Validate payload
    if (!payload.to || !payload.userName || !payload.planName || !payload.trialEndDate) {
      logger.error("Free trial reminder webhook: Missing required fields", { payload })
      return NextResponse.json({ success: false, error: "Missing required fields" }, { status: 400 })
    }

    // Check timestamp to prevent replay attacks (allow 5 minutes tolerance)
    const now = Date.now()
    const timeDiff = Math.abs(now - payload.timestamp)
    if (timeDiff > 5 * 60 * 1000) {
      logger.warn("Free trial reminder webhook: Request too old", {
        timestamp: payload.timestamp,
        now,
        diff: timeDiff,
      })
      return NextResponse.json({ success: false, error: "Request too old" }, { status: 400 })
    }

    logger.log("Processing free trial reminder webhook", {
      to: payload.to,
      userName: payload.userName,
      planName: payload.planName,
      trialEndDate: payload.trialEndDate,
    })

    // Send the email
    await sendFreeTrialReminderEmail({
      email: payload.to,
      userName: payload.userName,
      planName: payload.planName,
      trialEndDate: new Date(payload.trialEndDate),
      subscriptionId: "webhook-triggered", // We don't have subscription ID from webhook
    })

    logger.log("Free trial reminder email sent successfully via webhook", {
      to: payload.to,
      planName: payload.planName,
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error("Free trial reminder webhook error", { error: errorMessage })

    return NextResponse.json({ success: false, error: "Internal server error" }, { status: 500 })
  }
}
