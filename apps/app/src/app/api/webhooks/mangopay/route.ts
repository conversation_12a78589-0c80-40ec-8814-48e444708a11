import { NextRequest, NextResponse } from "next/server"

import { Locale } from "@/lib/i18n-config"
import { getPayInDetails, getRecurringRegistrationDetails } from "@/lib/mangopay"
import { MangopayWebhookBody } from "@/types/mangopay"
import { logger } from "@coheadcoaching/lib"
import { PaymentStatus, PrismaClient, SubscriptionStatus } from "@prisma/client"

import { sendPaymentNotificationEmail } from "./utils"

const prisma = new PrismaClient()

export async function POST(req: NextRequest) {
  try {
    const data = (await req.json()) as MangopayWebhookBody
    const eventType = data.EventType
    const resourceId = data.ResourceId

    logger.log(`Received Mangopay webhook: ${eventType} for ResourceId: ${resourceId}`)

    switch (eventType) {
      case "PAYIN_NORMAL_SUCCEEDED":
      case "PAYIN_NORMAL_CREATED": // Assuming CREATED might need similar handling initially
        {
          const payinDetails = await getPayInDetails(resourceId)

          if (payinDetails.Id !== resourceId) {
            logger.error("Webhook ResourceId mismatch with fetched PayIn details", {
              webhookId: resourceId,
              fetchedId: payinDetails.Id,
            })
            return NextResponse.json({ error: "Resource ID mismatch" }, { status: 400 })
          }

          if (payinDetails.Status !== "SUCCEEDED") {
            logger.warn(
              `PayIn status mismatch for SUCCEEDED event. Webhook: SUCCEEDED, Fetched: ${payinDetails.Status}`,
              { resourceId }
            )

            return NextResponse.json({ error: "PayIn status verification failed" }, { status: 400 })
          }

          const payment = await prisma.payment.findUnique({
            where: { mangopayPayinId: resourceId },
            include: {
              subscription: {
                include: {
                  user: true, // Include the user relation
                },
              },
            },
          })

          if (payment) {
            if (payment.status !== PaymentStatus.SUCCEEDED) {
              await prisma.payment.update({
                where: { id: payment.id },
                data: { status: PaymentStatus.SUCCEEDED, failureReason: null }, // Clear failure reason on success
              })
              logger.log(`Payment ${payment.id} updated to SUCCEEDED`)
            } else {
              logger.log(`Payment ${payment.id} already marked as SUCCEEDED`)
            }

            // Update subscription status if needed
            const subscription = await prisma.subscription.findUnique({ where: { id: payment.subscriptionId } })
            if (subscription && [SubscriptionStatus.PENDING, SubscriptionStatus.FAILED].includes(subscription.status)) {
              await prisma.subscription.update({
                where: { id: payment.subscriptionId },
                data: { status: SubscriptionStatus.ACTIVE },
              })
              logger.log(`Subscription ${payment.subscriptionId} updated to ACTIVE`)
            } else {
              logger.log(`Subscription ${payment.subscriptionId} status not updated (current: ${subscription?.status})`)
            }

            // Send email notification for successful payment verification
            // This happens regardless of whether the payment status was updated
            if (payment.subscription?.user) {
              const user = payment.subscription.user
              if (user && user.email) {
                try {
                  await sendPaymentNotificationEmail({
                    userEmail: user.email,
                    userName: user.name ?? "Unknown User",
                    userLocale: (user as { locale?: Locale }).locale ?? "en",
                    paymentStatus: "success",
                    paymentDetails: {
                      amount: payment.amount,
                      currency: payment.currency,
                      date: new Date(payment.updatedAt),
                    },
                  })
                  logger.log(`Payment success email sent for payment ${payment.id}`)
                } catch (error) {
                  // Email failures should not break webhook processing
                  logger.error(`Failed to send payment success email for payment ${payment.id}`, { error })
                }
              }
            }
          } else {
            logger.warn(`Payment not found for successful PayIn ${resourceId}`)
          }
        }
        break

      case "PAYIN_NORMAL_FAILED":
        {
          const payinDetails = await getPayInDetails(resourceId)

          if (payinDetails.Id !== resourceId) {
            logger.error("Webhook ResourceId mismatch with fetched PayIn details", {
              webhookId: resourceId,
              fetchedId: payinDetails.Id,
            })
            return NextResponse.json({ error: "Resource ID mismatch" }, { status: 400 })
          }

          if (payinDetails.Status !== "FAILED") {
            logger.warn(`PayIn status mismatch for FAILED event. Webhook: FAILED, Fetched: ${payinDetails.Status}`, {
              resourceId,
            })

            return NextResponse.json({ error: "PayIn status verification failed" }, { status: 400 })
          }

          const payment = await prisma.payment.findUnique({
            where: { mangopayPayinId: resourceId },
            include: {
              subscription: {
                include: {
                  user: true, // Include the user relation
                },
              },
            },
          })

          if (payment) {
            if (payment.status !== PaymentStatus.FAILED) {
              await prisma.payment.update({
                where: { id: payment.id },
                data: {
                  status: PaymentStatus.FAILED,
                  failureReason: payinDetails.ResultMessage || "Payment failed",
                },
              })
              logger.log(`Payment ${payment.id} updated to FAILED. Reason: ${payinDetails.ResultMessage}`)

              // Update subscription status to FAILED
              await prisma.subscription.update({
                where: { id: payment.subscriptionId },
                data: { status: SubscriptionStatus.FAILED },
              })
              logger.log(`Subscription ${payment.subscriptionId} updated to FAILED`)
            } else {
              logger.log(`Payment ${payment.id} already marked as FAILED`)
            }

            // Send email notification for failed payment verification
            // This happens regardless of whether the payment status was updated
            if (payment.subscription?.user) {
              const user = payment.subscription.user
              if (user && user.email) {
                try {
                  await sendPaymentNotificationEmail({
                    userEmail: user.email,
                    userName: user.name ?? "Unknown User",
                    userLocale: (user as { locale?: Locale }).locale ?? "en",
                    paymentStatus: "failed",
                    paymentDetails: {
                      amount: payment.amount,
                      currency: payment.currency,
                      date: new Date(payment.updatedAt),
                      failureReason: payinDetails.ResultMessage || "Payment failed",
                    },
                  })
                  logger.log(`Payment failure email sent for payment ${payment.id}`)
                } catch (error) {
                  // Email failures should not break webhook processing
                  logger.error(`Failed to send payment failure email for payment ${payment.id}`, { error })
                }
              }
            }
          } else {
            logger.warn(`Payment not found for failed PayIn ${resourceId}`)
          }
        }
        break

      case "RECURRING_REGISTRATION_CREATED":
      case "RECURRING_REGISTRATION_IN_PROGRESS":
        {
          const registrationDetails = await getRecurringRegistrationDetails(resourceId)

          if (registrationDetails.Id !== resourceId) {
            logger.error("Webhook ResourceId mismatch with fetched Recurring Registration details", {
              webhookId: resourceId,
              fetchedId: registrationDetails.Id,
            })
            return NextResponse.json({ error: "Resource ID mismatch" }, { status: 400 })
          }

          const expectedStatuses = ["CREATED", "IN_PROGRESS"]
          if (!expectedStatuses.includes(registrationDetails.Status)) {
            logger.warn(
              `Recurring Registration status mismatch for ${eventType} event. Fetched: ${registrationDetails.Status}`,
              { resourceId }
            )
            return NextResponse.json({ error: "Registration status verification failed" }, { status: 400 })
          }

          const subscription = await prisma.subscription.findUnique({
            where: { mangopayRecurringRegistrationId: resourceId },
          })

          if (subscription) {
            if (
              subscription.status !== SubscriptionStatus.PENDING &&
              subscription.status !== SubscriptionStatus.ACTIVE
            ) {
              await prisma.subscription.update({
                where: { id: subscription.id },
                data: { status: SubscriptionStatus.PENDING },
              })
              logger.log(
                `Subscription ${subscription.id} status updated to PENDING based on recurring registration ${eventType}.`
              )
            } else {
              logger.log(
                `Subscription ${subscription.id} found for recurring registration ${eventType}, status (${subscription.status}) not changed.`
              )
            }
          } else {
            logger.warn(`Subscription not found for recurring registration ${resourceId} event ${eventType}.`)
          }
        }
        break

      case "RECURRING_REGISTRATION_AUTH_NEEDED":
        {
          const registrationDetails = await getRecurringRegistrationDetails(resourceId)

          if (registrationDetails.Id !== resourceId) {
            logger.error("Webhook ResourceId mismatch with fetched Recurring Registration details", {
              webhookId: resourceId,
              fetchedId: registrationDetails.Id,
            })
            return NextResponse.json({ error: "Resource ID mismatch" }, { status: 400 })
          }

          if (registrationDetails.Status !== "AUTHENTICATION_NEEDED") {
            logger.warn(
              `Recurring Registration status mismatch for AUTH_NEEDED event. Fetched: ${registrationDetails.Status}`,
              { resourceId }
            )
            return NextResponse.json({ error: "Registration status verification failed" }, { status: 400 })
          }

          const subscription = await prisma.subscription.findUnique({
            where: { mangopayRecurringRegistrationId: resourceId },
          })

          if (subscription) {
            if (subscription.status !== SubscriptionStatus.PENDING) {
              await prisma.subscription.update({
                where: { id: subscription.id },
                data: { status: SubscriptionStatus.PENDING },
              })
              logger.log(`Subscription ${subscription.id} status updated to PENDING due to AUTH_NEEDED.`)
            } else {
              logger.log(`Subscription ${subscription.id} already PENDING, AUTH_NEEDED event received.`)
            }
          } else {
            logger.warn(`Subscription not found for recurring registration ${resourceId} event AUTH_NEEDED.`)
          }
        }
        break

      case "RECURRING_REGISTRATION_ENDED":
        {
          const registrationDetails = await getRecurringRegistrationDetails(resourceId)

          if (registrationDetails.Id !== resourceId) {
            logger.error("Webhook ResourceId mismatch with fetched Recurring Registration details", {
              webhookId: resourceId,
              fetchedId: registrationDetails.Id,
            })
            return NextResponse.json({ error: "Resource ID mismatch" }, { status: 400 })
          }

          if (registrationDetails.Status !== "ENDED") {
            logger.warn(
              `Recurring Registration status mismatch for ENDED event. Fetched: ${registrationDetails.Status}`,
              { resourceId }
            )
            return NextResponse.json({ error: "Registration status verification failed" }, { status: 400 })
          }

          const subscription = await prisma.subscription.findUnique({
            where: { mangopayRecurringRegistrationId: resourceId },
          })

          if (subscription) {
            if (
              subscription.status !== SubscriptionStatus.CANCELED &&
              subscription.status !== SubscriptionStatus.EXPIRED
            ) {
              await prisma.subscription.update({
                where: { id: subscription.id },
                data: { status: SubscriptionStatus.CANCELED, canceledAt: new Date() },
              })
              logger.log(
                `Subscription ${subscription.id} status updated to CANCELED due to RECURRING_REGISTRATION_ENDED.`
              )
            } else {
              logger.log(
                `Subscription ${subscription.id} already CANCELED/EXPIRED, RECURRING_REGISTRATION_ENDED event received.`
              )
            }
          } else {
            logger.warn(`Subscription not found for recurring registration ${resourceId} event ENDED.`)
          }
        }
        break

      default:
        logger.log(`Unhandled Mangopay event type: ${eventType}`)
        break
    }

    return NextResponse.json({ success: true })
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : String(error)
    logger.error(`Webhook processing error: ${message}`, { error })

    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
