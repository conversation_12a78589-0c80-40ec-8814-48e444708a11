import { getFullLogoUrl } from "@/constants/medias"
import { env } from "@/lib/env"
import { i18n, Locale } from "@/lib/i18n-config"
import { _getDictionary } from "@/lib/langs"
import { sendMail } from "@/lib/mailer"
import { logger } from "@coheadcoaching/lib"
import PaymentFailedEmail from "@coheadcoaching/transactional/emails/payment-failed" // You'll need to create these
import PaymentSuccessEmail from "@coheadcoaching/transactional/emails/payment-success" // You'll need to create these
import { render } from "@react-email/render"

export async function sendPaymentNotificationEmail({
  userEmail,
  userName,
  userLocale,
  paymentStatus,
  paymentDetails,
}: {
  userEmail: string
  userName: string
  userLocale: Locale
  paymentStatus: "success" | "failed"
  paymentDetails: {
    amount: number
    currency: string
    date: Date
    failureReason?: string
  }
}) {
  try {
    const locale = userLocale ?? i18n.defaultLocale
    const mailDict = await _getDictionary("transactionals", locale, {
      footer: true,
      paymentSuccess: paymentStatus === "success",
      paymentFailed: paymentStatus === "failed",
      // Add other required translation keys
    })

    const emailSubject = paymentStatus === "success" ? mailDict.paymentSuccess : mailDict.paymentFailed

    const emailComponent =
      paymentStatus === "success"
        ? PaymentSuccessEmail({
            footerText: mailDict.footer,
            previewText: "Votre paiement a été effectué avec succès",
            titleText: "Confirmation de paiement",
            contentTitle: "Votre paiement a été traité avec succès. Voici les détails de votre transaction :",
            heyText: "Bonjour",
            logoUrl: getFullLogoUrl(env.NEXT_PUBLIC_BASE_URL || ""),
            name: userName,
            amount: paymentDetails.amount,
            currency: paymentDetails.currency,
            date: paymentDetails.date,
            supportEmail: env.SUPPORT_EMAIL ?? "",
          })
        : PaymentFailedEmail({
            footerText: mailDict.footer,
            previewText: "Votre paiement a échoué",
            titleText: "Échec du paiement",
            contentTitle: "Nous n'avons pas pu traiter votre paiement. Voici les détails :",
            heyText: "Bonjour",
            logoUrl: getFullLogoUrl(env.NEXT_PUBLIC_BASE_URL || ""),
            name: userName,
            amount: paymentDetails.amount,
            currency: paymentDetails.currency,
            date: paymentDetails.date,
            failureReason: paymentDetails.failureReason || "",
            supportEmail: env.SUPPORT_EMAIL ?? "",
          })

    const text = render(emailComponent, { plainText: true })
    const html = render(emailComponent)

    await sendMail({
      to: userEmail,
      subject: emailSubject,
      text,
      html,
    })

    logger.log(`Payment ${paymentStatus} email sent to ${userEmail}`)
  } catch (error) {
    logger.error(`Failed to send payment ${paymentStatus} email`, { error })
  }
}
