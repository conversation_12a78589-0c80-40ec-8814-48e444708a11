import {
  changePasswordResponseSchema,
  changePasswordSchema,
  checkFrozenAccountRecoveryResponseSchema,
  checkFrozenAccountRecoverySchema,
  deleteAccountResponseSchema,
  deleteSessionResponseSchema,
  deleteSessionSchema,
  forgotPasswordResponseSchema,
  forgotPasswordSchema,
  getAccountResponseSchema,
  getActiveSessionsResponseSchema,
  getActiveSessionsSchema,
  recoverFrozenAccountResponseSchema,
  recoverFrozenAccountSchema,
  resetPasswordResponseSchema,
  resetPasswordSchema,
  sendVerificationEmailResponseSchema,
  sendVerificationEmailSchema,
  updateUserResponseSchema,
  updateUserSchema,
  verifyEmailResponseSchema,
  verifyEmailSchema,
} from "@/api/me/schemas"
import {
  authenticatedNoEmailVerificationProcedure,
  authenticatedProcedure,
  publicProcedure,
  router,
} from "@/lib/server/trpc"

import { sendVerificationEmail, verifyEmail } from "./email/mutations"
import { changePassword, forgotPassword, resetPassword } from "./password/mutations"
import { deleteSession } from "./sessions/mutations"
import { getActiveSessions } from "./sessions/queries"
import { checkFrozenAccountRecovery, deleteAccount, recoverFrozenAccount, updateUser } from "./mutations"
import { getAccount } from "./queries"

export const meRouter = router({
  updateUser: authenticatedProcedure.input(updateUserSchema()).output(updateUserResponseSchema()).mutation(updateUser),
  getActiveSessions: authenticatedNoEmailVerificationProcedure
    .input(getActiveSessionsSchema())
    .output(getActiveSessionsResponseSchema())
    .query(getActiveSessions),
  deleteSession: authenticatedNoEmailVerificationProcedure
    .input(deleteSessionSchema())
    .output(deleteSessionResponseSchema())
    .mutation(deleteSession),
  getAccount: authenticatedNoEmailVerificationProcedure.output(getAccountResponseSchema()).query(getAccount),
  deleteAccount: authenticatedNoEmailVerificationProcedure
    .output(deleteAccountResponseSchema())
    .mutation(deleteAccount),
  forgotPassword: publicProcedure
    .input(forgotPasswordSchema())
    .output(forgotPasswordResponseSchema())
    .mutation(forgotPassword),
  resetPassword: publicProcedure
    .input(resetPasswordSchema())
    .output(resetPasswordResponseSchema())
    .mutation(resetPassword),
  sendVerificationEmail: authenticatedNoEmailVerificationProcedure
    .input(sendVerificationEmailSchema())
    .output(sendVerificationEmailResponseSchema())
    .mutation(sendVerificationEmail),
  verifyEmail: publicProcedure.input(verifyEmailSchema()).output(verifyEmailResponseSchema()).mutation(verifyEmail),
  changePassword: authenticatedNoEmailVerificationProcedure
    .input(changePasswordSchema())
    .output(changePasswordResponseSchema())
    .mutation(changePassword),
  checkFrozenAccountRecovery: publicProcedure
    .input(checkFrozenAccountRecoverySchema())
    .output(checkFrozenAccountRecoveryResponseSchema())
    .mutation(async ({ input }) => {
      return await checkFrozenAccountRecovery({ email: input.email, password: input.password })
    }),
  recoverFrozenAccount: publicProcedure
    .input(recoverFrozenAccountSchema())
    .output(recoverFrozenAccountResponseSchema())
    .mutation(async ({ input }) => {
      return await recoverFrozenAccount({ userId: input.userId })
    }),
})
