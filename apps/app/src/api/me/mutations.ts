import { z } from "zod"

import { updateUserResponseSchema, updateUserSchema } from "@/api/me/schemas"
import { ACCOUNT_FREEZE_GRACE_PERIOD_DAYS, rolesAsObject } from "@/constants"
import { bcryptCompare } from "@/lib/bcrypt"
import { prisma } from "@/lib/prisma"
import { ApiError } from "@/lib/utils/server-utils"
import { ensureLoggedIn, handleApiError } from "@/lib/utils/server-utils"
import { apiInputFromSchema } from "@/types"
import { logger } from "@coheadcoaching/lib"
import { Prisma } from "@prisma/client"

export const updateUser = async ({ input, ctx: { session } }: apiInputFromSchema<typeof updateUserSchema>) => {
  ensureLoggedIn(session)
  try {
    const { username, profilePictureKey } = input

    const user = await prisma.user.findUnique({
      where: {
        id: session.user.id,
      },
      include: {
        profilePicture: true,
      },
    })
    if (!user) {
      return ApiError("userNotFound")
    }

    const getProfilePicture = async (key: string) => {
      const uploadingFile = await prisma.fileUploading.findUnique({
        where: {
          key,
        },
      })
      if (!uploadingFile) {
        return ApiError("fileNotFound")
      }

      return {
        bucket: uploadingFile.bucket,
        endpoint: uploadingFile.endpoint,
        key: uploadingFile.key,
        filetype: uploadingFile.filetype,
        fileUploading: {
          connect: {
            id: uploadingFile.id,
          },
        },
      }
    }

    const profilePicture =
      profilePictureKey === null || profilePictureKey === undefined
        ? profilePictureKey
        : await getProfilePicture(profilePictureKey)

    //* Disconnect old profile picture (when null or set)
    if (profilePictureKey !== undefined && user.profilePicture) {
      await prisma.user.update({
        where: {
          id: user.id,
        },
        data: {
          profilePictureId: null,
        },
      })
    }

    //* Update the user
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: {
        username,
        profilePicture:
          profilePicture !== undefined && profilePicture !== null
            ? {
                connectOrCreate: {
                  where: { key: profilePicture.key },
                  create: profilePicture,
                },
              }
            : undefined,
      },
      include: {
        profilePicture: true,
      },
    })

    const data: z.infer<ReturnType<typeof updateUserResponseSchema>> = {
      user: updatedUser,
    }
    return data
  } catch (error: unknown) {
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === "P2002") {
        const meta = error.meta
        if ((meta?.target as Array<string>).includes("username")) {
          return ApiError("username.exist")
        }
      }
    }
    return handleApiError(error)
  }
}

export const deleteAccount = async ({ ctx: { session } }: apiInputFromSchema<undefined>) => {
  try {
    ensureLoggedIn(session)
    //* Ensure not admin
    if (session.user.role === rolesAsObject.admin) {
      return ApiError("cannotDeleteAdmin", "FORBIDDEN")
    }

    //* Instead of deleting, freeze the account
    const user = await prisma.user.update({
      where: {
        id: session.user.id,
      },
      data: {
        isFrozen: true,
        frozenAt: new Date(),
      },
    })

    logger.log("Account frozen instead of deleted", { userId: session.user.id })

    return { user }
  } catch (error: unknown) {
    return handleApiError(error)
  }
}

/**
 * Check if an email corresponds to a frozen account that can be recovered
 */
export const checkFrozenAccountRecovery = async ({ email, password }: { email: string; password: string }) => {
  try {
    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        password: true,
        isFrozen: true,
        frozenAt: true,
      },
    })

    if (!user || !user.isFrozen || !user.frozenAt) {
      return { canRecover: false }
    }

    const isValidPassword = await bcryptCompare(password, user.password!)
    if (!isValidPassword) return { canRecover: false }

    // Check if within grace period
    const gracePeriodEnd = new Date(user.frozenAt)
    gracePeriodEnd.setDate(gracePeriodEnd.getDate() + ACCOUNT_FREEZE_GRACE_PERIOD_DAYS)

    if (new Date() > gracePeriodEnd) {
      return { canRecover: false }
    }

    return {
      canRecover: true,
      userId: user.id,
      email: user.email || undefined,
      gracePeriodEnd,
    }
  } catch (error: unknown) {
    logger.error("Error checking frozen account recovery", { email, error })
    return { canRecover: false }
  }
}

/**
 * Recover a frozen account within the grace period
 */
export const recoverFrozenAccount = async ({ userId }: { userId: string }) => {
  try {
    // Find the frozen user
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        isFrozen: true,
        frozenAt: true,
      },
    })

    if (!user) {
      return ApiError("userNotFound")
    }

    if (!user.isFrozen || !user.frozenAt) {
      return ApiError("forbidden")
    }

    // Check if within grace period
    const gracePeriodEnd = new Date(user.frozenAt)
    gracePeriodEnd.setDate(gracePeriodEnd.getDate() + ACCOUNT_FREEZE_GRACE_PERIOD_DAYS)

    if (new Date() > gracePeriodEnd) {
      return ApiError("forbidden")
    }

    // Unfreeze the account
    const recoveredUser = await prisma.user.update({
      where: { id: userId },
      data: {
        isFrozen: false,
        frozenAt: null,
      },
      select: {
        id: true,
        email: true,
        username: true,
        isFrozen: true,
      },
    })

    logger.log("Account recovered from frozen state", {
      userId,
      email: user.email,
      frozenAt: user.frozenAt,
      recoveredAt: new Date(),
    })

    return { user: recoveredUser, message: "Account successfully recovered" }
  } catch (error: unknown) {
    logger.error("Error recovering frozen account", { userId, error })
    return handleApiError(error)
  }
}
