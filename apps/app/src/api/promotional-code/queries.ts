import { z } from "zod"

import { prisma } from "@/lib/prisma"
import { ApiError, ensureLoggedIn, handleApiError } from "@/lib/utils/server-utils"
import { ITrpcContext } from "@/types"
import { logger } from "@coheadcoaching/lib"
import { Prisma } from "@prisma/client"

import {
  getPromotionalCodeSchema,
  listPromotionalCodesSchema,
  promotionalCodeListResponseSchema,
  promotionalCodeResponseSchema,
  promotionalCodeValidationResponseSchema,
  validatePromotionalCodeSchema,
} from "./schemas"

export const getPromotionalCode = async ({
  input,
  ctx: { session },
}: {
  input: z.infer<typeof getPromotionalCodeSchema>
  ctx: ITrpcContext
}) => {
  try {
    ensureLoggedIn(session)

    // Check if user has admin role
    if (!session.user.roles.includes("ADMIN")) {
      return ApiError("unauthorized", "FORBIDDEN")
    }

    const { id } = input

    const promotionalCode = await prisma.promotionalCode.findUnique({
      where: { id },
      include: {
        restrictedToPlans: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    if (!promotionalCode) {
      return ApiError("codeNotFound")
    }

    const data: z.infer<typeof promotionalCodeResponseSchema> = {
      id: promotionalCode.id,
      code: promotionalCode.code,
      name: promotionalCode.name,
      description: promotionalCode.description,
      effectType: promotionalCode.effectType,
      discountType: promotionalCode.discountType,
      discountValue: promotionalCode.discountValue,
      paymentCount: promotionalCode.paymentCount,
      isActive: promotionalCode.isActive,
      validFrom: promotionalCode.validFrom,
      validUntil: promotionalCode.validUntil,
      maxUsageCount: promotionalCode.maxUsageCount,
      currentUsageCount: promotionalCode.currentUsageCount,
      restrictedToPlans: promotionalCode.restrictedToPlans,
      createdAt: promotionalCode.createdAt,
      updatedAt: promotionalCode.updatedAt,
    }

    return data
  } catch (error: unknown) {
    return handleApiError(error)
  }
}

export const listPromotionalCodes = async ({
  input,
  ctx: { session },
}: {
  input: z.infer<typeof listPromotionalCodesSchema>
  ctx: ITrpcContext
}) => {
  try {
    ensureLoggedIn(session)

    // Check if user has admin role
    if (!session.user.roles.includes("ADMIN")) {
      return ApiError("unauthorized", "FORBIDDEN")
    }

    const { page, limit, search, isActive, effectType } = input
    const skip = (page - 1) * limit

    const where: Prisma.PromotionalCodeWhereInput = {}

    if (search) {
      where.OR = [
        { code: { contains: search, mode: "insensitive" } },
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ]
    }

    if (isActive !== undefined) {
      where.isActive = isActive
    }

    if (effectType) {
      where.effectType = effectType
    }

    const [codes, total] = await Promise.all([
      prisma.promotionalCode.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
        include: {
          restrictedToPlans: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      }),
      prisma.promotionalCode.count({ where }),
    ])

    const totalPages = Math.ceil(total / limit)

    const data: z.infer<typeof promotionalCodeListResponseSchema> = {
      codes: codes.map((code) => ({
        id: code.id,
        code: code.code,
        name: code.name,
        description: code.description,
        effectType: code.effectType,
        discountType: code.discountType,
        discountValue: code.discountValue,
        paymentCount: code.paymentCount,
        isActive: code.isActive,
        validFrom: code.validFrom,
        validUntil: code.validUntil,
        maxUsageCount: code.maxUsageCount,
        currentUsageCount: code.currentUsageCount,
        restrictedToPlans: code.restrictedToPlans,
        createdAt: code.createdAt,
        updatedAt: code.updatedAt,
      })),
      total,
      page,
      limit,
      totalPages,
    }

    return data
  } catch (error: unknown) {
    return handleApiError(error)
  }
}

export const validatePromotionalCode = async ({
  input,
  ctx: { session },
}: {
  input: z.infer<typeof validatePromotionalCodeSchema>
  ctx: ITrpcContext
}) => {
  try {
    const { code, planId, billingPeriod } = input

    // Find the promotional code
    const promotionalCode = await prisma.promotionalCode.findUnique({
      where: { code: code.toUpperCase() },
      include: {
        restrictedToPlans: true,
      },
    })

    const invalidCodeResponse: z.infer<typeof promotionalCodeValidationResponseSchema> = {
      isValid: false,
      error: "Ce code n'est pas valide",
    }

    if (!promotionalCode) {
      return invalidCodeResponse //? Code not found
    }

    // Check if code is active
    if (!promotionalCode.isActive) {
      return invalidCodeResponse //? Code is not active
    }

    // Check validity dates
    const now = new Date()
    if (promotionalCode.validFrom && now < promotionalCode.validFrom) {
      return invalidCodeResponse //? Code not yet active
    }

    if (promotionalCode.validUntil && now > promotionalCode.validUntil) {
      const data: z.infer<typeof promotionalCodeValidationResponseSchema> = {
        isValid: false,
        error: "Ce code a expiré",
      }
      return data
    }

    // Check usage limit
    if (promotionalCode.maxUsageCount && promotionalCode.currentUsageCount >= promotionalCode.maxUsageCount) {
      return invalidCodeResponse //? Code usage limit reached
    }

    // Check effect type compatibility with billing period
    if (
      (promotionalCode.effectType === "MONTHLY_ONLY" && billingPeriod !== "MONTHLY") ||
      (promotionalCode.effectType === "ANNUAL_ONLY" && billingPeriod !== "ANNUAL")
    ) {
      const data: z.infer<typeof promotionalCodeValidationResponseSchema> = {
        isValid: false,
        error: `Ce code ne s'applique pas à cette fréquence de paiement.`,
      }
      return data
    }

    // Check plan restrictions
    if (promotionalCode.restrictedToPlans.length > 0) {
      const isValidForPlan = promotionalCode.restrictedToPlans.some((plan) => plan.id === planId)
      if (!isValidForPlan) {
        const data: z.infer<typeof promotionalCodeValidationResponseSchema> = {
          isValid: false,
          error: "Ce code n'est pas valide pour votre offre.",
        }
        return data
      }
    }

    // Check if user has already used this code (if session exists)
    if (session?.user?.id) {
      const existingUsage = await prisma.promotionalCodeUsage.findUnique({
        where: {
          userId_promotionalCodeId: {
            userId: session.user.id,
            promotionalCodeId: promotionalCode.id,
          },
        },
      })

      if (existingUsage) {
        const data: z.infer<typeof promotionalCodeValidationResponseSchema> = {
          isValid: false,
          error: "Ce code a déjà été utilisé avec votre compte.",
        }
        return data
      }
    }

    // Code is valid
    const data: z.infer<typeof promotionalCodeValidationResponseSchema> = {
      isValid: true,
      code: promotionalCode.code,
      discountType: promotionalCode.discountType,
      discountValue: promotionalCode.discountValue,
      paymentCount: promotionalCode.paymentCount,
    }

    return data
  } catch (error: unknown) {
    logger.error("Error validating promotional code", error)
    const data: z.infer<typeof promotionalCodeValidationResponseSchema> = {
      isValid: false,
      error: "Une erreur est survenue lors de la vérification du code.",
    }
    return data
  }
}
