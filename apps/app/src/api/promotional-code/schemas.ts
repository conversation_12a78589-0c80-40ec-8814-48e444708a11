import { z } from "zod"

import {
  PROMOTIONAL_CODE_MAX_DISCOUNT_PERCENTAGE,
  PROMOTIONAL_CODE_MAX_LENGTH,
  PROMOTIONAL_CODE_MAX_PAYMENT_COUNT,
  PROMOTIONAL_CODE_MIN_LENGTH,
} from "@/constants"

// Enums
export const promotionalCodeEffectTypeSchema = z.enum(["MONTHLY_ONLY", "ANNUAL_ONLY", "BOTH"])
export const promotionalCodeDiscountTypeSchema = z.enum(["PERCENTAGE", "FIXED_AMOUNT"])

// Base schemas
export const createPromotionalCodeSchema = z.object({
  code: z
    .string()
    .min(PROMOTIONAL_CODE_MIN_LENGTH, `Le code doit contenir au moins ${PROMOTIONAL_CODE_MIN_LENGTH} caractères`)
    .max(PROMOTIONAL_CODE_MAX_LENGTH, `Le code doit contenir au maximum ${PROMOTIONAL_CODE_MAX_LENGTH} caractères`)
    .regex(
      /^[A-Z0-9_-]+$/,
      "Le code ne peut contenir que des lettres majuscules, des chiffres, des tirets bas et des tirets"
    ),
  name: z.string().min(1, "Le nom est requis").max(100, "Le nom doit contenir au maximum 100 caractères"),
  description: z.string().optional(),
  effectType: promotionalCodeEffectTypeSchema,
  discountType: promotionalCodeDiscountTypeSchema,
  discountValue: z.number().int().positive("La valeur de remise doit être positive"),
  paymentCount: z
    .number()
    .int()
    .min(1, "Le nombre de paiements doit être au moins 1")
    .max(
      PROMOTIONAL_CODE_MAX_PAYMENT_COUNT,
      `Le nombre de paiements doit être au maximum ${PROMOTIONAL_CODE_MAX_PAYMENT_COUNT}`
    ),
  validFrom: z.date().optional(),
  validUntil: z.date().optional(),
  maxUsageCount: z.number().int().min(0, "La limite d'utilisation doit être supérieure ou égale à 0").optional(),
  restrictedToPlanIds: z.array(z.number().int()).optional(),
})

export const updatePromotionalCodeSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Le nom est requis").max(100, "Le nom doit contenir au maximum 100 caractères").optional(),
  description: z.string().optional(),
  effectType: promotionalCodeEffectTypeSchema.optional(),
  discountType: promotionalCodeDiscountTypeSchema.optional(),
  discountValue: z.number().int().positive("La valeur de remise doit être positive").optional(),
  paymentCount: z
    .number()
    .int()
    .min(1, "Le nombre de paiements doit être au moins 1")
    .max(
      PROMOTIONAL_CODE_MAX_PAYMENT_COUNT,
      `Le nombre de paiements doit être au maximum ${PROMOTIONAL_CODE_MAX_PAYMENT_COUNT}`
    )
    .optional(),
  isActive: z.boolean().optional(),
  validFrom: z.date().optional(),
  validUntil: z.date().optional(),
  maxUsageCount: z.number().int().min(0, "La limite d'utilisation doit être supérieure ou égale à 0").optional(),
  restrictedToPlanIds: z.array(z.number().int()).optional(),
})

export const validatePromotionalCodeSchema = z.object({
  code: z.string(),
  planId: z.number().int(),
  billingPeriod: z.enum(["MONTHLY", "ANNUAL"]),
})

export const applyPromotionalCodeSchema = z.object({
  code: z.string(),
  subscriptionId: z.string(),
})

export const getPromotionalCodeSchema = z.object({
  id: z.string(),
})

export const listPromotionalCodesSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  search: z.string().optional(),
  isActive: z.boolean().optional(),
  effectType: promotionalCodeEffectTypeSchema.optional(),
})

export const deletePromotionalCodeSchema = z.object({
  id: z.string(),
})

// Response schemas
export const promotionalCodeResponseSchema = z.object({
  id: z.string(),
  code: z.string(),
  name: z.string(),
  description: z.string().nullable(),
  effectType: promotionalCodeEffectTypeSchema,
  discountType: promotionalCodeDiscountTypeSchema,
  discountValue: z.number(),
  paymentCount: z.number(),
  isActive: z.boolean(),
  validFrom: z.date().nullable(),
  validUntil: z.date().nullable(),
  maxUsageCount: z.number().nullable(),
  currentUsageCount: z.number(),
  restrictedToPlans: z.array(
    z.object({
      id: z.number(),
      name: z.string(),
    })
  ),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export const promotionalCodeValidationResponseSchema = z.object({
  isValid: z.boolean(),
  code: z.string().optional(),
  discountType: promotionalCodeDiscountTypeSchema.optional(),
  discountValue: z.number().optional(),
  paymentCount: z.number().optional(),
  error: z.string().optional(),
})

export const deletePromotionalCodeResponseSchema = z.object({
  success: z.boolean(),
})

export const promotionalCodeListResponseSchema = z.object({
  codes: z.array(promotionalCodeResponseSchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
})

// Custom validation
export const createPromotionalCodeSchemaWithValidation = createPromotionalCodeSchema
  .refine(
    (data) => {
      if (data.discountType === "PERCENTAGE" && data.discountValue > PROMOTIONAL_CODE_MAX_DISCOUNT_PERCENTAGE) {
        return false
      }
      return true
    },
    {
      message: `La remise en pourcentage ne peut pas dépasser ${PROMOTIONAL_CODE_MAX_DISCOUNT_PERCENTAGE}%`,
      path: ["discountValue"],
    }
  )
  .refine(
    (data) => {
      if (data.validFrom && data.validFrom < new Date()) {
        return false
      }
      return true
    },
    {
      message: "La date de début ne peut pas être dans le passé",
      path: ["validFrom"],
    }
  )
  .refine(
    (data) => {
      if (data.validFrom && data.validUntil && data.validFrom >= data.validUntil) {
        return false
      }
      return true
    },
    {
      message: "La date de fin doit être postérieure à la date de début",
      path: ["validUntil"],
    }
  )
  .refine(
    (data) => {
      // At least one of maxUsageCount or validUntil must be provided
      // Note: 0 is a valid usage count, so we check for null/undefined specifically
      const hasUsageCount = data.maxUsageCount !== null && data.maxUsageCount !== undefined
      const hasValidUntil = data.validUntil !== null && data.validUntil !== undefined

      if (!hasUsageCount && !hasValidUntil) {
        return false
      }
      return true
    },
    {
      message: "Une limite d'utilisation ou une date d'expiration doit être spécifiée",
      path: ["maxUsageCount"],
    }
  )

export const updatePromotionalCodeSchemaWithValidation = updatePromotionalCodeSchema
  .refine(
    (data) => {
      if (
        data.discountType === "PERCENTAGE" &&
        data.discountValue &&
        data.discountValue > PROMOTIONAL_CODE_MAX_DISCOUNT_PERCENTAGE
      ) {
        return false
      }
      return true
    },
    {
      message: `La remise en pourcentage ne peut pas dépasser ${PROMOTIONAL_CODE_MAX_DISCOUNT_PERCENTAGE}%`,
      path: ["discountValue"],
    }
  )
  .refine(
    (data) => {
      if (data.validFrom && data.validUntil && data.validFrom >= data.validUntil) {
        return false
      }
      return true
    },
    {
      message: "La date de fin doit être postérieure à la date de début",
      path: ["validUntil"],
    }
  )
  .refine(
    (data) => {
      // At least one of maxUsageCount or validUntil must be provided
      // Note: 0 is a valid usage count, so we check for null/undefined specifically
      const hasUsageCount = data.maxUsageCount !== null && data.maxUsageCount !== undefined
      const hasValidUntil = data.validUntil !== null && data.validUntil !== undefined

      if (!hasUsageCount && !hasValidUntil) {
        return false
      }
      return true
    },
    {
      message: "Une limite d'utilisation ou une date d'expiration doit être spécifiée",
      path: ["maxUsageCount"],
    }
  )
