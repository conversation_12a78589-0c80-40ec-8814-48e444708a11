import { z } from "zod"

import { prisma } from "@/lib/prisma"
import { iaBuilderAuthenticatedProcedure, router } from "@/lib/server/trpc"
import { TRPCError } from "@trpc/server"

const skillSchema = z.object({
  name: z.string(),
  agentId: z.number().optional(),
})

const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(50).default(6),
})

export const skillRouter = router({
  create: iaBuilderAuthenticatedProcedure.input(skillSchema).mutation(async ({ input }) => {
    const { name } = input

    // Check for duplicate skill name (case-insensitive)
    const existingSkill = await prisma.skill.findFirst({
      where: {
        name: {
          equals: name,
          mode: "insensitive",
        },
      },
    })

    if (existingSkill) {
      throw new TRPCError({ message: "Une compétence avec ce nom existe déjà", code: "BAD_REQUEST" })
    }

    return await prisma.skill.create({ data: input })
  }),

  update: iaBuilderAuthenticatedProcedure
    .input(z.object({ id: z.number(), name: z.string() }))
    .mutation(async ({ input }) => {
      const { id, name } = input

      // Check for duplicate skill name (case-insensitive), excluding current skill
      const existingSkill = await prisma.skill.findFirst({
        where: {
          name: {
            equals: name,
            mode: "insensitive",
          },
          id: {
            not: id,
          },
        },
      })

      if (existingSkill) {
        throw new TRPCError({ message: "Une compétence avec ce nom existe déjà", code: "BAD_REQUEST" })
      }

      return await prisma.skill.update({ where: { id: input.id }, data: { name: input.name } })
    }),

  delete: iaBuilderAuthenticatedProcedure.input(z.number()).mutation(async ({ input }) => {
    return await prisma.skill.delete({ where: { id: input } })
  }),

  getById: iaBuilderAuthenticatedProcedure.input(z.number()).query(async ({ input }) => {
    return await prisma.skill.findUnique({ where: { id: input } })
  }),

  getAll: iaBuilderAuthenticatedProcedure.query(async () => {
    return await prisma.skill.findMany()
  }),
  getAllForAdmin: iaBuilderAuthenticatedProcedure.input(paginationSchema).query(async ({ input }) => {
    const { page, pageSize } = input
    const skip = (page - 1) * pageSize

    const skills = await prisma.skill.findMany({
      skip,
      take: pageSize,
      include: {
        _count: {
          select: { agents: true },
        },
      },
    })

    const totalCount = await prisma.skill.count()

    return {
      data: skills,
      pagination: {
        page,
        pageSize,
        totalCount,
        totalPages: Math.ceil(totalCount / pageSize),
      },
    }
  }),
})
