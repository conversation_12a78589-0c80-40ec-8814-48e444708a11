"use server"

import { auth } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function toggleFavoriteAgent(agentId: number, isFavorite: boolean) {
  try {
    const session = await auth()

    if (!session?.user?.id) {
      throw new Error("User not authenticated")
    }
    const userId = session?.user?.id

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { favoriteAgentIds: true, isFrozen: true },
    })

    if (!user) throw new Error("Utilisateur introuvable")

    // Check if account is frozen
    if (user.isFrozen) {
      throw new Error("Votre compte est gelé. Veuillez contacter le support pour le récupérer.")
    }

    const updatedFavorites = new Set(user.favoriteAgentIds)

    if (isFavorite) {
      updatedFavorites.add(agentId)
    } else {
      updatedFavorites.delete(agentId)
    }

    await prisma.user.update({
      where: { id: userId },
      data: { favoriteAgentIds: Array.from(updatedFavorites) },
    })
    return true
  } catch (error) {
    console.error(`Erreur ${isFavorite ? "ajout" : "suppression"} favori :`, error)
    return false
  }
}
