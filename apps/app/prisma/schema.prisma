// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_PRISMA_URL") // uses connection pooling
  directUrl = env("DATABASE_URL_NON_POOLING") // uses a direct connection
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

enum UserRole {
  ADMIN
  USER
  SAV
  MODO
  IA_BUILDER
}

model User {
  id               String    @id @default(cuid())
  name             String?
  email            String?   @unique
  emailVerified    DateTime?
  profilePictureId String?   @unique
  profilePicture   File?     @relation(fields: [profilePictureId], references: [id], onDelete: SetNull)
  image            String? // Require to use auth.js
  accounts         Account[]

  // Custom fields
  username                   String?                     @unique
  role                       UserRole                    @default(USER) // Legacy role field, kept for backward compatibility
  roles                      UserRole[]                  @default([USER]) // New field for multiple roles
  password                   String?
  hasPassword                Boolean                     @default(false)
  resetPasswordToken         ResetPassordToken?
  userEmailVerificationToken UserEmailVerificationToken?
  lastLocale                 String?
  otpSecret                  String                      @default("")
  otpMnemonic                String                      @default("")
  otpVerified                Boolean                     @default(false)

  // Account freezing fields
  isFrozen          Boolean          @default(false)
  frozenAt          DateTime?
  hasUsedFreeTrial  Boolean          @default(false)
  uploadsInProgress FileUploading[]
  chats             Chat[]
  favoriteAgentIds  Int[]
  supportTickets    SupportTicket[]
  supportMessages   SupportMessage[]
  mangopayUserId    String?
  mangopayWalletId  String? // Only for payment receiver

  cards             MangopayCard[]
  subscriptions     Subscription[]
  categorySelection UserCategorySelection?
  refunds           Refund[]

  // Form system relations
  createdForms          Form[]                 @relation("FormCreator")
  formSubmissions       FormSubmission[]       @relation("FormSubmissions")
  promotionalCodeUsages PromotionalCodeUsage[]

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

//? For one time login links
model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([identifier, token])
}

model ResetPassordToken {
  identifier String   @unique
  token      String   @unique
  expires    DateTime
  user       User     @relation(fields: [identifier], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())
}

model UserEmailVerificationToken {
  identifier String   @unique
  token      String   @unique
  expires    DateTime
  user       User     @relation(fields: [identifier], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())
}

model File {
  id        String   @id @default(cuid())
  key       String   @unique
  filetype  String
  bucket    String
  endpoint  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userProfilePicture User?
  supportMessage     SupportMessage?

  fileUploadingId String?        @unique
  fileUploading   FileUploading? @relation(fields: [fileUploadingId], references: [id], onDelete: SetNull)
}

// Upload in progress
model FileUploading {
  id       String   @id @default(cuid())
  key      String   @unique
  filetype String
  bucket   String
  endpoint String
  expires  DateTime

  file File?

  userId String?
  user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

//Create new models Agent, Badge, Category, and Prompt to implement API functionalities

model Agent {
  id                     Int      @id @default(autoincrement())
  icon                   String
  title                  String
  description            String
  model                  String   @default("gpt-3.5-turbo")
  personality            String   @default("")
  temperature            Int      @default(1)
  additionalInstructions String   @default("")
  skills                 Skill[]  @relation("AgentSkills")
  prompts                Prompt[]
  badge                  Badge?   @relation(fields: [badgeId], references: [id])
  badgeId                Int?
  chats                  Chat[]
}

model Skill {
  id     Int     @id @default(autoincrement())
  name   String  @unique
  agents Agent[] @relation("AgentSkills") // Many-to-many relationship with Agent
}

model Badge {
  id                Int     @id @default(autoincrement())
  title             String
  // Color customization fields
  primaryColor      String  @default("#e4d4f4") // Hex color chosen by admin
  backgroundLight   String  @default("270 59.26% 89.41%") // HSL format for light theme background
  textLight         String  @default("270 66.67% 47.06%") // HSL format for light theme text
  backgroundDark    String  @default("270 66.67% 18.82%") // HSL format for dark theme background
  textDark          String  @default("270 59.26% 57.65%") // HSL format for dark theme text
  agents            Agent[]
}

model Prompt {
  id         Int       @id @default(autoincrement())
  title      String    @default("")
  body       String
  agentId    Int?
  agent      Agent?    @relation(fields: [agentId], references: [id])
  categoryId Int?
  category   Category? @relation(fields: [categoryId], references: [id])
}

model Category {
  id      Int      @id @default(autoincrement())
  name    String
  prompts Prompt[]
}

model Chat {
  id        String    @id @default(cuid())
  title     String?
  messages  Message[]
  userId    String // Reference to the user who owns this chat
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  agentId   Int // Reference to the agent this chat is with
  agent     Agent     @relation(fields: [agentId], references: [id])
  isSaved   Boolean   @default(false)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model Message {
  id        String   @id
  content   String   @db.Text
  role      String // "user" or "assistant" or "system"
  chatId    String
  chat      Chat     @relation(fields: [chatId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())
}

enum TicketStatus {
  OPEN
  IN_PROGRESS
  COMPLETED
}

model SupportTicket {
  id        String           @id @default(cuid())
  title     String
  status    TicketStatus     @default(OPEN)
  userId    String
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages  SupportMessage[]
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
}

model SupportMessage {
  id           String        @id @default(cuid())
  content      String
  ticketId     String
  ticket       SupportTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  senderId     String
  sender       User          @relation(fields: [senderId], references: [id], onDelete: Cascade)
  attachmentId String?       @unique
  attachment   File?         @relation(fields: [attachmentId], references: [id], onDelete: SetNull)
  createdAt    DateTime      @default(now())
}

model MangopayCard {
  id             String   @id @default(cuid())
  mangopayCardId String?  @unique // Rendu facultatif pour permettre la création temporaire
  userId         String
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  isDefault      Boolean  @default(true)
  last4          String?
  expirationDate String?
  isTemp         Boolean  @default(false) // Indique si c'est une carte temporaire en attente de l'ID MangoPay
  isActive       Boolean  @default(true)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
}

enum BillingPeriod {
  MONTHLY
  ANNUAL
}

enum RestrictionType {
  MAX_MESSAGES_PER_CHAT
  MAX_SAVED_CHATS
  MAX_AGENTS
  MAX_CATEGORIES
}

model PlanRestriction {
  id        String          @id @default(cuid())
  type      RestrictionType
  value     Int?
  planId    Int
  plan      Plan            @relation(fields: [planId], references: [id], onDelete: Cascade)
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt
}

model Plan {
  id                      Int               @id @default(autoincrement())
  name                    String
  description             String?
  monthlyPrice            Float
  annualPrice             Float
  monthlyRefundPercentage Int?
  annualRefundPercentage  Int?
  features                String[]
  isRecommended           Boolean           @default(false)
  isActive                Boolean           @default(true)
  freeTrialDays           Int? // Number of free trial days, null means no free trial
  subscriptions           Subscription[]
  restrictions            PlanRestriction[]
  refunds                 Refund[]
  promotionalCodes        PromotionalCode[]
  createdAt               DateTime          @default(now())
  updatedAt               DateTime          @updatedAt
}

model UserCategorySelection {
  id          String   @id @default(cuid())
  userId      String   @unique
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  categoryIds Int[] //? Reference to badges
  lastUpdated DateTime @default(now())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

enum SubscriptionStatus {
  ACTIVE
  CANCELED
  EXPIRED
  PENDING
  FAILED
  AUTHENTICATION_NEEDED
}

model Subscription {
  id             String             @id @default(cuid())
  userId         String
  user           User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  planId         Int
  plan           Plan               @relation(fields: [planId], references: [id])
  status         SubscriptionStatus @default(PENDING)
  billingPeriod  BillingPeriod
  startDate      DateTime
  endDate        DateTime
  canceledAt     DateTime?
  isAdminManaged Boolean            @default(false)

  // Free trial fields
  isFreeTrial       Boolean   @default(false)
  freeTrialEndDate  DateTime?
  trialReminderSent Boolean   @default(false)

  // Promotional code fields
  promotionalCodeId String?
  promotionalCode   PromotionalCode? @relation(fields: [promotionalCodeId], references: [id])

  payments                        Payment[]
  refunds                         Refund[]
  renewalLinks                    RenewalLink[]
  formSubmissions                 FormSubmission[]       @relation("FormSubmissions")
  promotionalCodeUsages           PromotionalCodeUsage[]
  createdAt                       DateTime               @default(now())
  updatedAt                       DateTime               @updatedAt
  mangopayRecurringRegistrationId String?                @unique
}

enum PaymentStatus {
  PENDING
  SUCCEEDED
  FAILED
  REFUNDED
}

model Payment {
  id              String        @id @default(cuid())
  subscriptionId  String
  subscription    Subscription  @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)
  amount          Float
  currency        String        @default("EUR")
  mangopayPayinId String?       @unique
  status          PaymentStatus @default(PENDING)
  failureReason   String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
}

enum RefundStatus {
  PENDING
  COMPLETED
  FAILED
}

enum RenewalLinkStatus {
  PENDING
  COMPLETED
  EXPIRED
}

model Refund {
  id               String        @id @default(cuid())
  userId           String
  user             User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  planId           Int
  plan             Plan          @relation(fields: [planId], references: [id])
  subscriptionId   String?
  subscription     Subscription? @relation(fields: [subscriptionId], references: [id], onDelete: SetNull)
  amount           Float // Amount in cents
  currency         String        @default("EUR")
  mangopayRefundId String?       @unique
  status           RefundStatus  @default(PENDING)
  failureReason    String?
  processedAt      DateTime?
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt
}

model RenewalLink {
  id             String            @id @default(cuid())
  token          String            @unique
  subscriptionId String
  subscription   Subscription      @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)
  status         RenewalLinkStatus @default(PENDING)
  expiresAt      DateTime
  completedAt    DateTime?
  browserInfo    Json? // Store browser information for compliance
  ipAddress      String?
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @updatedAt
}

// Form System Models

enum FormType {
  SUBSCRIPTION_CANCELLATION
  GENERAL_FEEDBACK
  SUPPORT_REQUEST
}

enum QuestionType {
  MULTIPLE_CHOICE // QCM - Multiple answers allowed
  SINGLE_CHOICE // QCU - Single answer only
  TEXT_SHORT // Short text input
  TEXT_LONG // Long text area
  EMAIL // Email validation
  NUMBER // Numeric input
  RATING // Rating scale (1-5, 1-10, etc.)
  YES_NO // Boolean question
  DATE // Date picker
}

enum FormStatus {
  DRAFT
  ACTIVE
  INACTIVE
  ARCHIVED
}

model Form {
  id          String     @id @default(cuid())
  title       String
  description String?    @db.Text
  type        FormType
  status      FormStatus @default(DRAFT)
  isActive    Boolean    @default(false) // Only one form per type can be active
  version     Int        @default(1)

  // Form configuration
  showProgressBar   Boolean @default(true)
  allowSaveProgress Boolean @default(false)
  requireAuth       Boolean @default(true)

  // Conditional logic settings
  enableConditionalLogic Boolean @default(false)

  // Questions and responses
  questions   FormQuestion[]
  submissions FormSubmission[]

  // Metadata
  createdBy String
  creator   User     @relation("FormCreator", fields: [createdBy], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Note: Unique constraint for active forms is handled by database-level partial unique index
  // This allows multiple inactive forms per type while ensuring only one active form per type
}

model FormQuestion {
  id     String @id @default(cuid())
  formId String
  form   Form   @relation(fields: [formId], references: [id], onDelete: Cascade)

  // Question content
  title       String
  description String?      @db.Text
  type        QuestionType
  order       Int // Display order

  // Validation rules
  isRequired Boolean @default(false)
  minLength  Int? // For text inputs
  maxLength  Int? // For text inputs
  minValue   Float? // For number/rating inputs
  maxValue   Float? // For number/rating inputs

  // Choice options (for MULTIPLE_CHOICE, SINGLE_CHOICE, RATING)
  options Json? // Array of {id, label, value} objects

  // Conditional logic
  showConditions Json? // Conditions to show this question

  // Responses
  responses FormResponse[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([formId, order]) // Ensure unique order within form
}

model FormSubmission {
  id     String @id @default(cuid())
  formId String
  form   Form   @relation(fields: [formId], references: [id], onDelete: Cascade)

  // User information
  userId String? // Null for anonymous submissions
  user   User?   @relation("FormSubmissions", fields: [userId], references: [id], onDelete: SetNull)

  // Submission metadata
  isComplete  Boolean   @default(false)
  submittedAt DateTime?
  ipAddress   String?
  userAgent   String?   @db.Text

  // Associated cancellation (if applicable)
  subscriptionId String? // Link to subscription being cancelled
  subscription   Subscription? @relation("FormSubmissions", fields: [subscriptionId], references: [id], onDelete: SetNull)

  // Responses
  responses FormResponse[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model FormResponse {
  id           String         @id @default(cuid())
  submissionId String
  submission   FormSubmission @relation(fields: [submissionId], references: [id], onDelete: Cascade)
  questionId   String
  question     FormQuestion   @relation(fields: [questionId], references: [id], onDelete: Cascade)

  // Response data
  textValue       String?   @db.Text // For text responses
  numberValue     Float? // For numeric responses
  booleanValue    Boolean? // For yes/no responses
  dateValue       DateTime? // For date responses
  selectedOptions Json? // For choice responses - array of selected option IDs

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([submissionId, questionId]) // One response per question per submission
}

// Promotional Codes System
enum PromotionalCodeEffectType {
  MONTHLY_ONLY
  ANNUAL_ONLY
  BOTH
}

enum PromotionalCodeDiscountType {
  PERCENTAGE
  FIXED_AMOUNT
}

model PromotionalCode {
  id          String  @id @default(cuid())
  code        String  @unique
  name        String // Admin-friendly name
  description String? @db.Text

  // Effect configuration
  effectType    PromotionalCodeEffectType // Which billing periods this applies to
  discountType  PromotionalCodeDiscountType // Percentage or fixed amount
  discountValue Int // Percentage (1-100) or amount in cents
  paymentCount  Int                         @default(1) // Number of payments the discount applies to

  // Validity and usage
  isActive          Boolean   @default(true)
  validFrom         DateTime? // When the code becomes valid
  validUntil        DateTime? // When the code expires
  maxUsageCount     Int? // Maximum total uses across all users
  currentUsageCount Int       @default(0) // Current usage count

  // Plan restrictions
  restrictedToPlans Plan[] // Plans this code can be applied to

  // Relations
  subscriptions Subscription[]
  usages        PromotionalCodeUsage[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model PromotionalCodeUsage {
  id                String          @id @default(cuid())
  userId            String
  user              User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  promotionalCodeId String
  promotionalCode   PromotionalCode @relation(fields: [promotionalCodeId], references: [id], onDelete: Cascade)
  subscriptionId    String?
  subscription      Subscription?   @relation(fields: [subscriptionId], references: [id], onDelete: SetNull)

  usedAt DateTime @default(now())

  @@unique([userId, promotionalCodeId]) // Each user can only use a code once
}
