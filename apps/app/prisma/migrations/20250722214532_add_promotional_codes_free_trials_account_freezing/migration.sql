-- CreateE<PERSON>
CREATE TYPE "PromotionalCodeEffectType" AS ENUM ('MONTHLY_ONLY', 'ANNUAL_ONLY', 'BOTH');

-- CreateEnum
CREATE TYPE "PromotionalCodeDiscountType" AS ENUM ('PERCENTAGE', 'FIXED_AMOUNT');

-- AlterTable
ALTER TABLE "Plan" ADD COLUMN     "freeTrialDays" INTEGER;

-- AlterTable
ALTER TABLE "Subscription" ADD COLUMN     "freeTrialEndDate" TIMESTAMP(3),
ADD COLUMN     "isFreeTrial" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "promotionalCodeId" TEXT,
ADD COLUMN     "trialReminderSent" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "frozenAt" TIMESTAMP(3),
ADD COLUMN     "hasUsedFreeTrial" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "isFrozen" BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "PromotionalCode" (
    "id" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "effectType" "PromotionalCodeEffectType" NOT NULL,
    "discountType" "PromotionalCodeDiscountType" NOT NULL,
    "discountValue" INTEGER NOT NULL,
    "paymentCount" INTEGER NOT NULL DEFAULT 1,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "validFrom" TIMESTAMP(3),
    "validUntil" TIMESTAMP(3),
    "maxUsageCount" INTEGER,
    "currentUsageCount" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PromotionalCode_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PromotionalCodeUsage" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "promotionalCodeId" TEXT NOT NULL,
    "subscriptionId" TEXT,
    "usedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PromotionalCodeUsage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_PlanToPromotionalCode" (
    "A" INTEGER NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "PromotionalCode_code_key" ON "PromotionalCode"("code");

-- CreateIndex
CREATE UNIQUE INDEX "PromotionalCodeUsage_userId_promotionalCodeId_key" ON "PromotionalCodeUsage"("userId", "promotionalCodeId");

-- CreateIndex
CREATE UNIQUE INDEX "_PlanToPromotionalCode_AB_unique" ON "_PlanToPromotionalCode"("A", "B");

-- CreateIndex
CREATE INDEX "_PlanToPromotionalCode_B_index" ON "_PlanToPromotionalCode"("B");

-- AddForeignKey
ALTER TABLE "Subscription" ADD CONSTRAINT "Subscription_promotionalCodeId_fkey" FOREIGN KEY ("promotionalCodeId") REFERENCES "PromotionalCode"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromotionalCodeUsage" ADD CONSTRAINT "PromotionalCodeUsage_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromotionalCodeUsage" ADD CONSTRAINT "PromotionalCodeUsage_promotionalCodeId_fkey" FOREIGN KEY ("promotionalCodeId") REFERENCES "PromotionalCode"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromotionalCodeUsage" ADD CONSTRAINT "PromotionalCodeUsage_subscriptionId_fkey" FOREIGN KEY ("subscriptionId") REFERENCES "Subscription"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_PlanToPromotionalCode" ADD CONSTRAINT "_PlanToPromotionalCode_A_fkey" FOREIGN KEY ("A") REFERENCES "Plan"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_PlanToPromotionalCode" ADD CONSTRAINT "_PlanToPromotionalCode_B_fkey" FOREIGN KEY ("B") REFERENCES "PromotionalCode"("id") ON DELETE CASCADE ON UPDATE CASCADE;
