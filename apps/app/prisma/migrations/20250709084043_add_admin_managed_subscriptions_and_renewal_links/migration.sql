-- CreateEnum
CREATE TYPE "RenewalLinkStatus" AS ENUM ('PENDING', 'COMPLETED', 'EXPIRED');

-- AlterEnum
ALTER TYPE "SubscriptionStatus" ADD VALUE 'AUTHENTICATION_NEEDED';

-- AlterTable
ALTER TABLE "Subscription" ADD COLUMN     "isAdminManaged" BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "RenewalLink" (
    "id" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "subscriptionId" TEXT NOT NULL,
    "status" "RenewalLinkStatus" NOT NULL DEFAULT 'PENDING',
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "completedAt" TIMESTAMP(3),
    "browserInfo" JSONB,
    "ipAddress" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "RenewalLink_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "RenewalLink_token_key" ON "RenewalLink"("token");

-- AddForeignKey
ALTER TABLE "RenewalLink" ADD CONSTRAINT "RenewalLink_subscriptionId_fkey" FOREIGN KEY ("subscriptionId") REFERENCES "Subscription"("id") ON DELETE CASCADE ON UPDATE CASCADE;
