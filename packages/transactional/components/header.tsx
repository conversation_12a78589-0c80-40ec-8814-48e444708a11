import React from "react"

import { Container, Text } from "@react-email/components"

import { primary } from "../constants"

import { Logo } from "./logo"

export const Header = ({ logoUrl: _, titleText }: { logoUrl: string; titleText: string }) => {
  return (
    <Container style={headerContainer}>
      <Logo />
      <Text style={title}>{titleText}</Text>
    </Container>
  )
}

const headerContainer = {
  textAlign: "center",
  marginBottom: "24px",
} as const

const title = {
  fontSize: "24px",
  lineHeight: 1.25,
  fontWeight: "bold",
  color: primary,
  textAlign: "center",
  margin: "0",
} as const
