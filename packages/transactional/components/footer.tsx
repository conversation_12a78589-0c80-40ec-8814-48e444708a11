import React from "react"

import { Container, Hr, Text } from "@react-email/components"

import { mutedForeground, primary } from "../constants"

import { Link } from "./link"
import { Logo } from "./logo"

export const Footer = ({
  supportEmail,
  footerText,
  logoUrl: _,
}: {
  supportEmail: string
  footerText: string
  logoUrl: string
}) => {
  const curYear = new Date().getFullYear()

  return (
    <Container style={footer}>
      <Text style={footerTextStyle}>
        {footerText}{" "}
        <Link
          href={`mailto:${supportEmail}`}
          style={{
            textDecoration: "underline",
            color: primary,
            fontWeight: "bold",
          }}
        >
          {supportEmail}
        </Link>
      </Text>
      <Hr style={hrStyle} />
      <Container style={footerLogoContainer}>
        <Logo />
        <Text style={subFooterTextStyle}>© {curYear} CoheadCoaching - Tous droits réservés</Text>
      </Container>
    </Container>
  )
}

const footer = {
  color: mutedForeground,
  fontSize: "14px",
  textAlign: "center",
  marginTop: "60px",
  padding: "20px 0",
} as const

const footerTextStyle = {
  lineHeight: "1.5",
  margin: "0 0 20px 0",
} as const

const hrStyle = {
  marginTop: "24px",
  borderColor: mutedForeground,
  opacity: 0.15,
  marginBottom: "24px",
} as const

const footerLogoContainer = {
  textAlign: "center",
  margin: "0 auto",
} as const

const subFooterTextStyle = {
  fontSize: "12px",
  margin: "12px 0 0 0",
  color: mutedForeground,
  textAlign: "center",
} as const
