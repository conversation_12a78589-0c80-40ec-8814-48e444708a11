import { Head, Html, Preview, Text } from "@react-email/components"

import { Body } from "../components/body"
import { Button } from "../components/button"
import { Card } from "../components/card"
import { Container } from "../components/container"
import { Footer } from "../components/footer"
import { Header } from "../components/header"
import HeyText from "../components/hey-text"

interface PaymentFailedProps {
  previewText: string
  logoUrl: string
  name: string
  supportEmail: string
  titleText: string
  footerText: string
  contentTitle: string
  heyText: string
  amount: number
  currency: string
  date: Date
  failureReason: string
  retryUrl?: string
}

export const PaymentFailed = ({
  previewText,
  logoUrl,
  name,
  supportEmail,
  titleText,
  footerText,
  contentTitle,
  heyText,
  amount,
  currency,
  date,
  failureReason,
  retryUrl,
}: PaymentFailedProps) => (
  <Html>
    <Head />
    <Preview>{previewText}</Preview>
    <Body>
      <Container>
        <Header logoUrl={logoUrl} titleText={titleText} />
        <Card>
          <HeyText heyText={heyText} name={name} />
          <Text style={text}>{contentTitle}</Text>

          <Text style={paymentDetails}>
            <strong>Montant :</strong> {(amount / 100).toFixed(2)} {currency}
            <br />
            <strong>Date :</strong> {date.toLocaleDateString("fr-FR")}
            <br />
            <strong>Statut :</strong> Échec
            <br />
            <strong>Raison :</strong> {failureReason}
          </Text>

          <Text style={failureMessage}>
            Nous n'avons pas pu traiter votre paiement. Veuillez vérifier vos informations de paiement et réessayer.
          </Text>

          {retryUrl && <Button href={retryUrl}>Retry Payment</Button>}
        </Card>
        <Footer supportEmail={supportEmail} footerText={footerText} logoUrl={logoUrl} />
      </Container>
    </Body>
  </Html>
)

export const previewProps: PaymentFailedProps = {
  logoUrl: "/logo.svg",
  name: "John Doe",
  previewText: "Votre paiement a échoué",
  supportEmail: "<EMAIL>",
  titleText: "Échec du paiement",
  footerText:
    "Cet e-mail vous a été envoyé dans le cadre de nos services de paiement. Si vous avez des questions, veuillez nous contacter à",
  contentTitle: "Nous n'avons pas pu traiter votre paiement. Voici les détails :",
  heyText: "Hey",
  amount: 9999,
  currency: "EUR",
  date: new Date(),
  failureReason: "Fonds insuffisants",
  retryUrl: "https://coheadcoaching.com/payment/retry/abc123",
}

PaymentFailed.PreviewProps = previewProps

export default PaymentFailed

const text = {
  margin: "0 0 10px 0",
  textAlign: "left",
} as const

const paymentDetails = {
  margin: "15px 0",
  lineHeight: "1.5",
  textAlign: "left",
  backgroundColor: "rgb(245, 245, 245)",
  padding: "16px",
  borderRadius: "8px",
  border: "1px solid rgb(235, 235, 235)",
} as const

const failureMessage = {
  margin: "20px 0 0 0",
  textAlign: "center",
  color: "rgb(243, 18, 96)",
  fontWeight: "600",
  fontSize: "16px",
} as const
