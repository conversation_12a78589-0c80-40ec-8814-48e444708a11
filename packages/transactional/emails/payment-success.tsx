import { Head, Html, Preview, Text } from "@react-email/components"

import { Body } from "../components/body"
import { Card } from "../components/card"
import { Container } from "../components/container"
import { Footer } from "../components/footer"
import { Header } from "../components/header"
import HeyText from "../components/hey-text"

interface PaymentSuccessProps {
  previewText: string
  logoUrl: string
  name: string
  supportEmail: string
  titleText: string
  footerText: string
  contentTitle: string
  heyText: string
  amount: number
  currency: string
  date: Date
  planName?: string
  subscriptionId?: string
}

export const PaymentSuccess = ({
  previewText,
  logoUrl,
  name,
  supportEmail,
  titleText,
  footerText,
  contentTitle,
  heyText,
  amount,
  currency,
  date,
  planName,
  subscriptionId,
}: PaymentSuccessProps) => (
  <Html>
    <Head />
    <Preview>{previewText}</Preview>
    <Body>
      <Container>
        <Header logoUrl={logoUrl} titleText={titleText} />
        <Card>
          <HeyText heyText={heyText} name={name} />
          <Text style={text}>{contentTitle}</Text>

          <Text style={paymentDetails}>
            <strong>Montant :</strong> {(amount / 100).toFixed(2)} {currency}
            <br />
            <strong>Date :</strong> {date.toLocaleDateString("fr-FR")}
            <br />
            <strong>Statut :</strong> Confirmé
            {planName && (
              <>
                <br />
                <strong>Plan :</strong> {planName}
              </>
            )}
            {subscriptionId && (
              <>
                <br />
                <strong>ID Abonnement :</strong> {subscriptionId}
              </>
            )}
          </Text>

          <Text style={successMessage}>
            Votre paiement a été traité avec succès. Vous pouvez maintenant profiter pleinement de votre abonnement.
          </Text>
        </Card>
        <Footer supportEmail={supportEmail} footerText={footerText} logoUrl={logoUrl} />
      </Container>
    </Body>
  </Html>
)

export const previewProps: PaymentSuccessProps = {
  logoUrl: "/logo.svg",
  name: "John Doe",
  previewText: "Votre paiement a été effectué avec succès",
  supportEmail: "<EMAIL>",
  titleText: "Confirmation de paiement",
  footerText:
    "Cet e-mail vous a été envoyé dans le cadre de nos services de paiement. Si vous avez des questions, veuillez nous contacter à",
  contentTitle: "Votre paiement a été traité avec succès. Voici les détails de votre transaction :",
  heyText: "Hey",
  amount: 9999,
  currency: "EUR",
  date: new Date(),
  planName: "Plan Premium",
  subscriptionId: "sub_123456789",
}

PaymentSuccess.PreviewProps = previewProps

export default PaymentSuccess

const text = {
  margin: "0 0 10px 0",
  textAlign: "left",
} as const

const paymentDetails = {
  margin: "15px 0",
  lineHeight: "1.5",
  textAlign: "left",
  backgroundColor: "rgb(245, 245, 245)",
  padding: "16px",
  borderRadius: "8px",
  border: "1px solid rgb(235, 235, 235)",
} as const

const successMessage = {
  margin: "20px 0 0 0",
  textAlign: "center",
  color: "rgb(253, 108, 52)",
  fontWeight: "600",
  fontSize: "16px",
} as const
