import { <PERSON>, <PERSON><PERSON>, Container, Head, Hr, Html, Preview, Section, Text } from "@react-email/components"

import { Footer } from "../components/footer"
import { Header } from "../components/header"

interface FreeTrialReminderEmailProps {
  userName: string
  planName: string
  trialEndDate: string
  manageSubscriptionUrl: string
  logoUrl: string
  supportEmail: string
}

export const FreeTrialReminderEmail = ({
  userName = "Utilisateur",
  planName = "Plan Premium",
  trialEndDate = "15 janvier 2024",
  manageSubscriptionUrl = "https://coheadcoaching.com/profile/subscription",
  logoUrl = "https://coheadcoaching.com/logo.svg",
  supportEmail,
}: FreeTrialReminderEmailProps) => {
  const previewText = `Votre essai gratuit ${planName} se termine bientôt`

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Header logoUrl={logoUrl} titleText={"Votre essai gratuit se termine bientôt"} />

          <Text style={paragraph}>Bonjour {userName},</Text>

          <Text style={paragraph}>
            Nous espérons que vous appréciez votre essai gratuit du plan <strong>{planName}</strong> !
          </Text>

          <Text style={paragraph}>
            Votre période d'essai gratuit se terminera le <strong>{trialEndDate}</strong>. Après cette date, votre
            abonnement sera automatiquement activé et votre méthode de paiement sera débitée.
          </Text>

          <Section style={buttonContainer}>
            <Button style={button} href={manageSubscriptionUrl}>
              Gérer mon abonnement
            </Button>
          </Section>

          <Text style={paragraph}>
            Si vous souhaitez annuler votre abonnement avant la fin de la période d'essai, vous pouvez le faire à tout
            moment depuis votre espace personnel.
          </Text>

          <Hr style={hr} />

          <Footer
            supportEmail={supportEmail}
            footerText={"Vous recevez cet email car vous avez un essai gratuit actif sur CoheadCoaching."}
            logoUrl={logoUrl}
          />
        </Container>
      </Body>
    </Html>
  )
}

export default FreeTrialReminderEmail

const main = {
  backgroundColor: "#ffffff",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
}

const container = {
  margin: "0 auto",
  padding: "20px 0 48px",
  maxWidth: "560px",
}

const paragraph = {
  margin: "0 0 15px",
  fontSize: "15px",
  lineHeight: "1.4",
  color: "rgb(148, 148, 148)", // default500 color from constants
}

const buttonContainer = {
  textAlign: "center" as const,
  margin: "32px 0",
}

const button = {
  backgroundColor: "rgb(253, 108, 52)", // primary color from constants
  borderRadius: "12px",
  fontWeight: "600",
  color: "#ffffff",
  fontSize: "15px",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  padding: "12px 24px",
}

const hr = {
  borderColor: "rgb(211, 211, 211)", // default200 color from constants
  margin: "20px 0",
}
