import * as React from "react"

import { Head, Html, Preview, Text } from "@react-email/components"

import { Body } from "../components/body"
import { Button } from "../components/button"
import { Card } from "../components/card"
import { Container } from "../components/container"
import { Footer } from "../components/footer"
import { Header } from "../components/header"
import HeyText from "../components/hey-text"

interface SubscriptionRenewalLinkProps {
  renewalLink: string
  previewText: string
  logoUrl: string
  name: string
  supportEmail: string
  titleText: string
  footerText: string
  contentTitle: string
  actionText: string
  heyText: string
  planName: string
  billingPeriod: string
  expirationDate: string
  warningText: string
  instructionsText: string
}

export const SubscriptionRenewalLink = ({
  renewalLink,
  previewText,
  logoUrl,
  name,
  supportEmail,
  titleText,
  footerText,
  contentTitle,
  actionText,
  heyText,
  planName,
  billingPeriod,
  expirationDate,
  warningText,
  instructionsText,
}: SubscriptionRenewalLinkProps) => (
  <Html>
    <Head />
    <Preview>{previewText}</Preview>
    <Body>
      <Container>
        <Header logoUrl={logoUrl} titleText={titleText} />
        <Card>
          <HeyText heyText={heyText} name={name} />
          <Text style={text}>{contentTitle}</Text>

          <Text style={subscriptionDetails}>
            <strong>Plan :</strong> {planName}
            <br />
            <strong>Période de facturation :</strong> {billingPeriod}
            <br />
            <strong>Action requise avant le :</strong> {expirationDate}
          </Text>

          <Text style={warningStyle}>{warningText}</Text>

          <Button href={renewalLink}>{actionText}</Button>

          <Text style={instructionsStyle}>{instructionsText}</Text>
        </Card>
        <Footer supportEmail={supportEmail} footerText={footerText} logoUrl={logoUrl} />
      </Container>
    </Body>
  </Html>
)

export const previewProps: SubscriptionRenewalLinkProps = {
  logoUrl: "/logo.svg",
  name: "John Doe",
  previewText: "Action requise : Renouvellement de votre abonnement",
  supportEmail: "<EMAIL>",
  renewalLink: "https://coheadcoaching.com/subscription/renew/abc123",
  titleText: "Renouvellement d'abonnement requis",
  footerText:
    "Cet e-mail vous a été envoyé dans le cadre de nos services d'abonnement. Si vous avez des questions, veuillez nous contacter à",
  contentTitle:
    "Votre abonnement nécessite une action de votre part pour continuer. Une re-authentification bancaire est requise pour maintenir votre service actif.",
  actionText: "Renouveler mon abonnement",
  heyText: "Bonjour",
  planName: "Plan Premium",
  billingPeriod: "Mensuel",
  expirationDate: "15 janvier 2024",
  warningText:
    "⚠️ Important : Si aucune action n'est effectuée avant la date d'expiration, votre abonnement sera suspendu et vous perdrez l'accès à nos services.",
  instructionsText:
    "En cliquant sur le bouton ci-dessus, vous serez redirigé vers une page sécurisée où vous pourrez confirmer le renouvellement de votre abonnement. Cette étape est nécessaire pour respecter les réglementations bancaires européennes (PSD2).",
}

SubscriptionRenewalLink.PreviewProps = previewProps

export default SubscriptionRenewalLink

const text = {
  margin: "0 0 10px 0",
  textAlign: "left",
} as const

const subscriptionDetails = {
  margin: "20px 0",
  padding: "15px",
  backgroundColor: "rgb(245, 245, 245)",
  borderRadius: "8px",
  border: "1px solid rgb(235, 235, 235)",
  fontSize: "14px",
  lineHeight: "1.5",
} as const

const warningStyle = {
  margin: "20px 0",
  padding: "15px",
  backgroundColor: "rgb(254, 249, 195)",
  borderRadius: "8px",
  border: "1px solid rgb(253, 230, 138)",
  fontSize: "14px",
  lineHeight: "1.5",
  color: "rgb(146, 64, 14)",
} as const

const instructionsStyle = {
  margin: "20px 0 0 0",
  fontSize: "12px",
  lineHeight: "1.4",
  color: "rgb(148, 148, 148)",
  textAlign: "left",
} as const
